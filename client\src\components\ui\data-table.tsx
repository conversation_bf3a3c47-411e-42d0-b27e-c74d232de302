"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const data = [
  {
    id: "m5gr84i9",
    amount: 316,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "3u1reuv4",
    amount: 242,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "derv1ws0",
    amount: 837,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "5kma53ae",
    amount: 874,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "bhqecj4p",
    amount: 721,
    status: "failed",
    email: "<EMAIL>",
  },
]

export default function DataTableDemo() {
  return (
    <div className="w-full font-base text-main-foreground">
      <div className="flex items-center py-4">
        <h3 className="text-sm font-heading">Sample Data Table</h3>
      </div>
      <div>
        <Table>
          <TableHeader className="font-heading">
            <TableRow className="bg-secondary-background text-foreground">
              <TableHead className="text-foreground">ID</TableHead>
              <TableHead className="text-foreground">Status</TableHead>
              <TableHead className="text-foreground">Email</TableHead>
              <TableHead className="text-foreground text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row) => (
              <TableRow
                className="bg-secondary-background text-foreground"
                key={row.id}
              >
                <TableCell className="px-4 py-2 font-mono text-xs">{row.id}</TableCell>
                <TableCell className="px-4 py-2">
                  <span className={`capitalize px-2 py-1 rounded text-xs ${
                    row.status === 'success' ? 'bg-green-100 text-green-800' :
                    row.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                    row.status === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {row.status}
                  </span>
                </TableCell>
                <TableCell className="px-4 py-2 lowercase">{row.email}</TableCell>
                <TableCell className="px-4 py-2 text-right font-base">
                  {new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: "USD",
                  }).format(row.amount)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="text-foreground flex-1 text-sm">
          {data.length} row(s) total.
        </div>
      </div>
    </div>
  )
}
