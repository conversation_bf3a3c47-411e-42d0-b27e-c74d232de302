import { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Download,
  MessageCircle,
  X,
  BarChart3,
  TrendingUp,
  Calendar,
  FileText,
  PanelRightOpen,
  PanelRightClose,
  Info,
  Activity,
  MapPin,
  Users,
  Bot,
  Loader2,
  Code
} from "lucide-react";
import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "./ui/card";
import { Badge } from "./ui/badge";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "./ui/tabs";
import InteractiveMap from "./InteractiveMap";
import { createNewSession, sendChatMessage, type ChatResponse } from "../lib/api";

// District data interface from CSV - matching InteractiveMap
interface DistrictData {
  serialNo: string;
  state: string;
  district: string;
  rainfallTotal: number;
  geoAreaRechargeTotal: number;
  geoAreaTotal: number;
  gwRechargeTotal: number;
  annualResTotal: number;
  gwExtractionTotal: number;
  soe: number;
  category: string;
}

const MapPage = () => {
  const navigate = useNavigate();
  const [selectedState, setSelectedState] = useState<string | null>(null);
  const [selectedDistrict, setSelectedDistrict] = useState<DistrictData | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [aiResponse, setAiResponse] = useState<ChatResponse | null>(null);
  const [isLoadingAI, setIsLoadingAI] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Color mapping for categories - matching InteractiveMap
  const CATEGORY_COLORS: { [key: string]: string } = {
    "Safe": "#22c55e",           // Green
    "Semi-Critical": "#5c5b59ff",  // Yellow
    "Critical": "#f97316",       // Orange
    "Over-exploited": "#dc2626", // Red
    "no-data": "#6b7280",        // Grey
  };

  const getStatusColor = (category: string) => {
    return CATEGORY_COLORS[category] || CATEGORY_COLORS["no-data"];
  };

  // Handle district selection from InteractiveMap
  const handleDistrictSelect = (district: DistrictData | null) => {
    setSelectedDistrict(district);
    setAiResponse(null); // Clear previous AI response when selecting new district
    if (district) {
      setSidebarOpen(true); // Auto-open sidebar when district is selected
    }
  };

  // AI Query Function
  const handleAskAI = async (districtName: string) => {
    try {
      setIsLoadingAI(true);
      setActiveTab("chat"); // Switch to chat tab immediately

      // Create session if not exists
      let currentSessionId = sessionId;
      if (!currentSessionId) {
        const newSession = await createNewSession();
        currentSessionId = newSession.session_id;
        setSessionId(currentSessionId);
      }

      // Send query to AI
      const query = `Tell me more about groundwater conditions, trends, and recommendations for ${districtName} district`;
      const response = await sendChatMessage({
        question: query,
        session_id: currentSessionId,
        include_visualization: true
      });

      setAiResponse(response);
    } catch (error) {
      console.error('Error querying AI:', error);
      // You could add error state handling here if needed
    } finally {
      setIsLoadingAI(false);
    }
  };

  return (
    <div className="absolute top-[70px] left-0 right-0 bottom-0 bg-background overflow-hidden">
      {/* Main Content - Neobrutalism Layout */}
      <div className="flex relative w-full h-full">
        {/* Map Container - Responsive Width */}
        <div className={`${sidebarOpen ? 'w-2/3' : 'w-full'} relative transition-all duration-300 ease-in-out h-full`}>
          <div className="w-full h-full">
            <InteractiveMap onDistrictSelect={handleDistrictSelect} />
          </div>

          {/* Sidebar Toggle Button - Neobrutalism Style */}
          <div className="absolute top-4 right-4 z-10">
            <Button
              variant="default"
              size="icon"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="shadow-shadow hover:translate-x-boxShadowX hover:translate-y-boxShadowY hover:shadow-none"
            >
              {sidebarOpen ? <PanelRightClose className="h-4 w-4" /> : <PanelRightOpen className="h-4 w-4" />}
            </Button>
          </div>

          {/* Map Legend - Neobrutalism Style */}
          <Card className="absolute top-4 left-4 z-10 w-64 shadow-shadow border-2 border-border">
            <CardHeader className="pb-3 border-b-2 border-border">
              <CardTitle className="text-sm font-heading">Groundwater Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 pt-4">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-safe rounded-base border-2 border-border"></div>
                <span className="text-sm font-base">Safe (&gt;70%)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-semi-critical rounded-base border-2 border-border"></div>
                <span className="text-sm font-base">Semi-Critical (50-70%)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-critical rounded-base border-2 border-border"></div>
                <span className="text-sm font-base">Critical (30-50%)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-over-exploited rounded-base border-2 border-border"></div>
                <span className="text-sm font-base">Over-Exploited (&lt;30%)</span>
              </div>
            </CardContent>
          </Card>

          {/* State Summary Panel - Neobrutalism Style */}
          {selectedState && (
            <motion.div
              initial={{ opacity: 0, x: -300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -300 }}
              className="absolute bottom-4 left-4 z-10"
            >
              <Card className="w-80 shadow-shadow border-2 border-border">
                <CardHeader className="flex flex-row items-center justify-between pb-3 border-b-2 border-border">
                  <CardTitle className="text-lg font-heading">{selectedState}</CardTitle>
                  <Button
                    variant="neutral"
                    size="sm"
                    onClick={() => setSelectedState(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="bg-secondary-background rounded-base border-2 border-border shadow-shadow p-3">
                      <p className="text-foreground/60 text-xs font-base">Districts</p>
                      <p className="font-heading text-lg">142</p>
                    </div>
                    <div className="bg-secondary-background rounded-base border-2 border-border shadow-shadow p-3">
                      <p className="text-foreground/60 text-xs font-base">Critical Areas</p>
                      <p className="font-heading text-lg text-critical">23</p>
                    </div>
                    <div className="bg-secondary-background rounded-base border-2 border-border shadow-shadow p-3">
                      <p className="text-foreground/60 text-xs font-base">Safe Areas</p>
                      <p className="font-heading text-lg text-safe">89</p>
                    </div>
                    <div className="bg-secondary-background rounded-base border-2 border-border shadow-shadow p-3">
                      <p className="text-foreground/60 text-xs font-base">Trend</p>
                      <p className="font-heading text-lg text-critical">↓ 1.2%</p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      onClick={() => navigate(`/chatbot?q=Tell me about groundwater in ${selectedState}`)}
                      className="flex-1"
                    >
                      <MessageCircle className="mr-2 h-3 w-3" />
                      Ask AI
                    </Button>
                    <Button variant="neutral" size="sm">
                      <Download className="mr-2 h-3 w-3" />
                      Export
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </div>

        {/* Enhanced District Details Sidebar */}
        {sidebarOpen && selectedDistrict && (
          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            className="w-1/3 bg-secondary-background border-l-2 border-border shadow-shadow overflow-hidden flex flex-col"
          >
              {/* Enhanced Sidebar Header */}
              <div className="p-6 border-b-2 border-border bg-background">
                <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-base bg-main border-2 border-border flex items-center justify-center">
                      <MapPin className="h-4 w-4 text-main-foreground" />
                    </div>
                  <div>
                      <h2 className="text-lg font-heading">{selectedDistrict.district}</h2>
                      <p className="text-sm text-foreground/60 font-base">{selectedDistrict.state}</p>
                  </div>
                </div>
                <Button
                  variant="neutral"
                  size="sm"
                  onClick={() => setSidebarOpen(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

                {/* Status Badge */}
                <div className="flex justify-center">
                  <Badge
                    className="text-sm font-heading border-2 border-border rounded-base px-6 py-2 text-black"
                    style={{ backgroundColor: getStatusColor(selectedDistrict.category) }}
                  >
                    {selectedDistrict.category}
                  </Badge>
                    </div>
                  </div>
                  
              {/* Enhanced Tabbed Content */}
              <div className="flex-1 p-6 overflow-y-auto scrollbar">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-4 mb-6">
                    <TabsTrigger value="overview" className="text-xs px-2 py-2">
                      <Info className="h-3 w-3 mr-1" />
                      Overview
                    </TabsTrigger>
                    <TabsTrigger value="metrics" className="text-xs px-2 py-2">
                      <BarChart3 className="h-3 w-3 mr-1" />
                      Metrics
                    </TabsTrigger>
                    <TabsTrigger value="trends" className="text-xs px-2 py-2">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Trends
                    </TabsTrigger>
                    <TabsTrigger value="chat" className="text-xs px-2 py-2">
                      <Bot className="h-3 w-3 mr-1" />
                      Chat
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-4">
                    {/* Rainfall Data */}
                    <Card className="bg-main text-main-foreground">
                      <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                        <CardTitle className="text-sm">Rainfall Data</CardTitle>
                      </CardHeader>
                      <CardContent className="pt-6 px-6 pb-6">
                        <div className="text-center">
                          <div className="text-2xl font-heading mb-2">{selectedDistrict.rainfallTotal.toFixed(1)}</div>
                          <div className="text-xs opacity-90">Total Rainfall (mm)</div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Geographic Area */}
                    <Card>
                      <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                        <CardTitle className="text-sm">Geographic Area (sq km)</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3 pt-6 px-6 pb-6">
                        <div className="flex justify-between items-center py-2">
                          <span className="text-xs text-foreground/60 font-base">Recharge Area:</span>
                          <span className="text-sm font-heading">{selectedDistrict.geoAreaRechargeTotal.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center py-2">
                          <span className="text-xs text-foreground/60 font-base">Total Area:</span>
                          <span className="text-sm font-heading">{selectedDistrict.geoAreaTotal.toLocaleString()}</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Additional Information */}
                    {selectedDistrict.serialNo && (
                      <Card>
                        <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                          <CardTitle className="text-sm">Additional Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3 pt-6 px-6 pb-6">
                          <div className="flex justify-between items-center py-2">
                            <span className="text-xs text-foreground/60 font-base">Serial No:</span>
                            <span className="text-sm font-heading">{selectedDistrict.serialNo}</span>
                          </div>
                          <div className="flex justify-between items-center py-2">
                            <span className="text-xs text-foreground/60 font-base">State:</span>
                            <span className="text-sm font-heading">{selectedDistrict.state}</span>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </TabsContent>

                  <TabsContent value="metrics" className="space-y-4">
                    {/* Groundwater Resources */}
                    <Card>
                      <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                        <CardTitle className="text-sm">Groundwater Resources (MCM)</CardTitle>
                        <CardDescription className="text-xs">Million Cubic Meters</CardDescription>
                      </CardHeader>
                      <CardContent className="pt-6 px-6 pb-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                            <div className="text-lg font-heading text-main mb-2">{selectedDistrict.gwRechargeTotal.toLocaleString()}</div>
                            <div className="text-xs text-foreground/60">Total Recharge</div>
                          </div>
                          <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                            <div className="text-lg font-heading mb-2">{selectedDistrict.annualResTotal.toLocaleString()}</div>
                            <div className="text-xs text-foreground/60">Annual Recharge</div>
                          </div>
                          <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                            <div className="text-lg font-heading mb-2">{selectedDistrict.gwExtractionTotal.toLocaleString()}</div>
                            <div className="text-xs text-foreground/60">Total Extraction</div>
                      </div>
                          <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                            <div className="text-lg font-heading text-critical mb-2">{selectedDistrict.soe.toFixed(1)}%</div>
                            <div className="text-xs text-foreground/60">Stage of Extraction</div>
                    </div>
                  </div>
                      </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <div className="grid grid-cols-2 gap-4 px-2">
                      <Button variant="neutral" size="sm" className="h-10 py-2">
                        <Download className="mr-2 h-4 w-4" />
                        Export Data
                      </Button>
                      <Button variant="neutral" size="sm" className="h-10 py-2">
                        <FileText className="mr-2 h-4 w-4" />
                        Generate Report
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="trends" className="space-y-4">
                    {/* Extraction vs Recharge Analysis */}
                    <Card>
                      <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                        <CardTitle className="text-sm">Extraction vs Recharge Analysis</CardTitle>
                        <CardDescription className="text-xs">Sustainability indicators</CardDescription>
                      </CardHeader>
                      <CardContent className="pt-6 px-6 pb-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between p-4 bg-background rounded-base border-2 border-border">
                            <div className="flex items-center space-x-3">
                              <Activity className="h-4 w-4 text-main" />
                              <span className="text-sm font-base">Stage of Extraction</span>
                            </div>
                            <span className={`text-sm font-heading ${selectedDistrict.soe > 70 ? 'text-critical' : selectedDistrict.soe > 50 ? 'text-orange-500' : 'text-safe'}`}>
                              {selectedDistrict.soe.toFixed(1)}%
                            </span>
                </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                              <p className="text-lg font-heading text-safe mb-2">{selectedDistrict.gwRechargeTotal.toLocaleString()}</p>
                              <p className="text-xs text-foreground/60">Total Recharge (MCM)</p>
                      </div>
                            <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                              <p className="text-lg font-heading text-critical mb-2">{selectedDistrict.gwExtractionTotal.toLocaleString()}</p>
                              <p className="text-xs text-foreground/60">Total Extraction (MCM)</p>
                    </div>
                  </div>
                  
                          {/* Status Indicator */}
                          <div className="text-center p-5 bg-background rounded-base border-2 border-border">
                            <p className="text-xs text-foreground/60 mb-3">Groundwater Status</p>
                            <p className="text-sm font-heading" style={{ color: getStatusColor(selectedDistrict.category) }}>
                              {selectedDistrict.category}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="chat" className="space-y-4">
                    {/* AI Chat Response */}
                    {!aiResponse && !isLoadingAI && (
                      <Card>
                        <CardContent className="p-6 text-center">
                          <div className="w-12 h-12 bg-main rounded-base border-2 border-border flex items-center justify-center mx-auto mb-4">
                            <Bot className="h-6 w-6 text-main-foreground" />
                          </div>
                          <h3 className="text-lg font-heading mb-3">AI Analysis</h3>
                          <p className="text-sm text-foreground/60 font-base mb-4">
                            Click "Ask AI" to get detailed insights about {selectedDistrict.district} district's groundwater conditions.
                          </p>
                        </CardContent>
                      </Card>
                    )}

                    {isLoadingAI && (
                      <Card>
                        <CardContent className="p-6 text-center">
                          <div className="w-12 h-12 bg-main rounded-base border-2 border-border flex items-center justify-center mx-auto mb-4">
                            <Loader2 className="h-6 w-6 text-main-foreground animate-spin" />
                          </div>
                          <h3 className="text-lg font-heading mb-3">Analyzing...</h3>
                          <p className="text-sm text-foreground/60 font-base">
                            AI is analyzing groundwater data for {selectedDistrict.district} district...
                          </p>
                        </CardContent>
                      </Card>
                    )}

                    {aiResponse && (
                      <div className="space-y-4">
                        {/* AI Response Header */}
                        <div className="text-center p-4 bg-main/10 rounded-base border-2 border-border">
                          <div className="w-10 h-10 bg-main rounded-base border-2 border-border flex items-center justify-center mx-auto mb-3">
                            <Bot className="h-5 w-5 text-main-foreground" />
                          </div>
                          <h3 className="text-lg font-heading mb-2">AI Analysis Complete</h3>
                          <p className="text-sm text-foreground/60 font-base">
                            Analysis for {selectedDistrict.district} district
                          </p>
                        </div>

                        {/* Subsection Tabs for AI Response */}
                        <Tabs defaultValue="explain" className="w-full">
                          <TabsList className="grid w-full grid-cols-3 mb-4">
                            <TabsTrigger value="explain" className="text-xs px-2 py-2">
                              <MessageCircle className="h-3 w-3 mr-1" />
                              Explain
                            </TabsTrigger>
                            <TabsTrigger value="rawdata" className="text-xs px-2 py-2">
                              <BarChart3 className="h-3 w-3 mr-1" />
                              Raw Data
                            </TabsTrigger>
                            <TabsTrigger value="sql" className="text-xs px-2 py-2">
                              <Code className="h-3 w-3 mr-1" />
                              SQL
                            </TabsTrigger>
                          </TabsList>

                          {/* Explain Tab */}
                          <TabsContent value="explain" className="space-y-4">
                            <Card>
                              <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                                <CardTitle className="text-sm">AI Insights & Recommendations</CardTitle>
                                <CardDescription className="text-xs">Generated analysis and suggestions</CardDescription>
                              </CardHeader>
                              <CardContent className="pt-6 px-6 pb-6">
                                <div className="prose prose-sm max-w-none">
                                  <div className="text-sm font-base whitespace-pre-wrap leading-relaxed">
                                    {aiResponse.response}
                                  </div>
                                </div>
                                {aiResponse.explanation && (
                                  <div className="mt-4 p-4 bg-background rounded-base border-2 border-border">
                                    <h4 className="text-sm font-heading mb-2">Technical Explanation</h4>
                                    <div className="text-xs font-base text-foreground/80">
                                      {aiResponse.explanation}
                                    </div>
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          </TabsContent>

                          {/* Raw Data Tab */}
                          <TabsContent value="rawdata" className="space-y-4">
                            {aiResponse.data && aiResponse.data.length > 0 ? (
                              <Card>
                                <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                                  <CardTitle className="text-sm">Data Results</CardTitle>
                                  <CardDescription className="text-xs">
                                    {aiResponse.metadata?.rows_returned || aiResponse.data.length} rows returned
                                    {aiResponse.metadata?.execution_time && ` • Executed in ${aiResponse.metadata.execution_time}`}
                                  </CardDescription>
                                </CardHeader>
                                <CardContent className="pt-6 px-6 pb-6">
                                  <div className="overflow-x-auto">
                                    <table className="w-full text-xs">
                                      <thead>
                                        <tr className="border-b border-border">
                                          {Object.keys(aiResponse.data[0]).map((key) => (
                                            <th key={key} className="text-left p-2 font-heading bg-background">
                                              {key}
                                            </th>
                                          ))}
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {aiResponse.data.slice(0, 15).map((row, index) => (
                                          <tr key={index} className="border-b border-border/50 hover:bg-background/50">
                                            {Object.values(row).map((value, cellIndex) => (
                                              <td key={cellIndex} className="p-2 font-base">
                                                {String(value)}
                                              </td>
                                            ))}
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                    {aiResponse.data.length > 15 && (
                                      <p className="text-xs text-foreground/60 mt-3 text-center p-2 bg-background rounded border">
                                        Showing first 15 of {aiResponse.data.length} rows
                                      </p>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                            ) : (
                              <Card>
                                <CardContent className="p-6 text-center">
                                  <p className="text-sm text-foreground/60">No data returned from the query</p>
                                </CardContent>
                              </Card>
                            )}
                          </TabsContent>

                          {/* SQL Tab */}
                          <TabsContent value="sql" className="space-y-4">
                            {aiResponse.sql_query ? (
                              <Card>
                                <CardHeader className="pb-4 border-b-2 border-border px-6 py-4">
                                  <CardTitle className="text-sm">SQL Query</CardTitle>
                                  <CardDescription className="text-xs">
                                    Database query used for analysis
                                    {aiResponse.metadata?.columns && ` • ${aiResponse.metadata.columns.length} columns`}
                                  </CardDescription>
                                </CardHeader>
                                <CardContent className="pt-6 px-6 pb-6">
                                  <div className="bg-background p-4 rounded-base border-2 border-border">
                                    <pre className="text-xs font-mono text-foreground/90 whitespace-pre-wrap overflow-x-auto">
                                      {aiResponse.sql_query}
                                    </pre>
                                  </div>
                                  {aiResponse.metadata && (
                                    <div className="mt-4 grid grid-cols-2 gap-4">
                                      <div className="p-3 bg-background rounded-base border border-border">
                                        <p className="text-xs font-heading mb-1">Execution Time</p>
                                        <p className="text-sm font-base">{aiResponse.metadata.execution_time || 'N/A'}</p>
                      </div>
                                      <div className="p-3 bg-background rounded-base border border-border">
                                        <p className="text-xs font-heading mb-1">Rows Returned</p>
                                        <p className="text-sm font-base">{aiResponse.metadata.rows_returned || 0}</p>
                    </div>
                  </div>
                                  )}
                                </CardContent>
                              </Card>
                            ) : (
                              <Card>
                                <CardContent className="p-6 text-center">
                                  <p className="text-sm text-foreground/60">No SQL query available</p>
                                </CardContent>
                              </Card>
                            )}
                          </TabsContent>
                        </Tabs>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
                </div>

              {/* Enhanced AI Button - Fixed at Bottom */}
              <div className="p-6 border-t-2 border-border bg-background">
                <Button
                  className="w-full text-sm h-12 font-base py-3"
                  onClick={() => handleAskAI(selectedDistrict.district)}
                  disabled={isLoadingAI}
                >
                  {isLoadingAI ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <MessageCircle className="mr-2 h-4 w-4" />
                  )}
                  {isLoadingAI ? 'Asking AI...' : `Ask AI about ${selectedDistrict.district}`}
                  </Button>
              </div>
            </motion.div>
          )}

        {/* Enhanced Default Sidebar - When open but no district selected */}
        {sidebarOpen && !selectedDistrict && (
          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            className="w-1/3 bg-secondary-background border-l-2 border-border shadow-shadow overflow-hidden flex flex-col"
          >
            {/* Enhanced Default Header */}
            <div className="p-6 border-b-2 border-border bg-background">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-base bg-main border-2 border-border flex items-center justify-center">
                    <MapPin className="h-4 w-4 text-main-foreground" />
                  </div>
                  <div>
                    <h2 className="text-lg font-heading">District Explorer</h2>
                    <p className="text-sm text-foreground/60 font-base">Groundwater Analysis</p>
                  </div>
                </div>
                <Button
                  variant="neutral"
                  size="sm"
                  onClick={() => setSidebarOpen(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
            {/* Enhanced Default Content */}
            <div className="flex-1 p-6 space-y-6">
              {/* Welcome Card */}
              <Card className="bg-main text-main-foreground">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-secondary-background border-2 border-border rounded-base mx-auto mb-4 flex items-center justify-center">
                    <BarChart3 className="h-6 w-6 text-main" />
                  </div>
                  <h3 className="text-lg font-heading mb-3">Select a District</h3>
                  <p className="text-sm opacity-90 font-base">
                    Click on any district on the map to explore detailed groundwater data and insights.
                  </p>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader className="pb-4 px-6 py-4">
                  <CardTitle className="text-sm">Quick Overview</CardTitle>
                  <CardDescription className="text-xs">National groundwater statistics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 px-6 pb-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                      <p className="text-lg font-heading text-safe mb-2">89</p>
                      <p className="text-xs text-foreground/60">Safe Districts</p>
                    </div>
                    <div className="text-center p-4 bg-background rounded-base border-2 border-border">
                      <p className="text-lg font-heading text-critical mb-2">23</p>
                      <p className="text-xs text-foreground/60">Critical Areas</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Instructions */}
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-3 text-sm font-base text-foreground/80">
                    <p className="py-1">• Click districts for detailed analysis</p>
                    <p className="py-1">• Use map controls to navigate</p>
                    <p className="py-1">• Export data for reports</p>
                    <p className="py-1">• Ask AI for insights</p>
                  </div>
                </CardContent>
              </Card>
              </div>
            </motion.div>
          )}
      </div>
    </div>
  );
};

export default MapPage;