"use client"

import type React from "react"

import { useState } from "react"
import { Upload, Send, TrendingUp, BarChart3 } from "lucide-react"
// @ts-ignore
import Plot from "react-plotly.js"

interface PlotlyData {
  data: any
  layout: any
}

interface EDAResults {
  plotly_json?: PlotlyData | PlotlyData[]
  [key: string]: any
}

const EDAPage = () => {
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvContent, setCsvContent] = useState<string>("")
  const [userQuery, setUserQuery] = useState<string>("")
  const [results, setResults] = useState<EDAResults | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string>("")

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0]
    if (file && file.type === "text/csv") {
      setCsvFile(file)
      setError("")
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result ?? ""
        setCsvContent(typeof result === "string" ? result : "")
      }
      reader.readAsText(file)
    } else {
      setError("Please upload a valid CSV file")
    }
  }

  const handleSubmit = async () => {
    if (!csvContent || !userQuery.trim()) {
      setError("Please upload a CSV file and enter a query")
      return
    }

    setLoading(true)
    setError("")

    try {
      // @ts-ignore
      const baseUrl = import.meta.env.VITE_API_BASE_URL
      const response = await fetch(baseUrl + "/eda", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          csv_content: csvContent,
          user_query: userQuery.trim(),
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setResults(data)
    } catch (err) {
      let message = "Unknown error"
      if (err instanceof Error) {
        message = err.message
      } else if (typeof err === "string") {
        message = err
      }
      setError("Error analyzing data: " + message)
    } finally {
      setLoading(false)
    }
  }

  // Helper to format primitive values nicely
  const formatPrimitive = (val: any) => {
    if (val === null || val === undefined) return "—"
    if (typeof val === "number") {
      try {
        return new Intl.NumberFormat(undefined, {
          maximumFractionDigits: 4,
        }).format(val)
      } catch {
        return String(val)
      }
    }
    if (typeof val === "boolean") return val ? "Yes" : "No"
    return String(val)
  }

  // Recursively render arrays/objects into readable text (no JSON)
  const renderValue = (value: any): React.ReactNode => {
    // Arrays
    if (Array.isArray(value)) {
      const isPrimitiveArray = value.every((v) => v === null || ["string", "number", "boolean"].includes(typeof v))
      if (isPrimitiveArray) {
        return (
          <p className="text-sm text-gray-800 leading-relaxed">{value.map((v) => formatPrimitive(v)).join(", ")}</p>
        )
      }
      // Array of objects/mixed: show up to 5 items, indent nested
      const shown = value.slice(0, 5)
      return (
        <div className="space-y-2">
          {shown.map((item, i) => (
            <div key={i} className="pl-3 border-l border-black/10">
              {renderValue(item)}
            </div>
          ))}
          {value.length > 5 && <p className="text-xs text-gray-600">{`+${value.length - 5} more`}</p>}
        </div>
      )
    }

    // Objects
    if (typeof value === "object" && value) {
      const entries = Object.entries(value)
      if (entries.length === 0) return <p className="text-sm text-gray-500">Empty</p>

      // Split primitives vs nested to keep it clean
      const primitiveRows = entries.filter(([, v]) => v === null || ["string", "number", "boolean"].includes(typeof v))
      const nestedRows = entries.filter(([, v]) => v && typeof v === "object")

      return (
        <div className="space-y-2 text-left">
          {/* Primitive key/value pairs */}
          {primitiveRows.length > 0 && (
            <div className="space-y-1">
              {primitiveRows.map(([k, v]) => (
                <p key={k} className="text-sm leading-relaxed break-words">
                  <span className="font-semibold capitalize">{k.replace(/_/g, " ")}</span>
                  {": "}
                  <span className="font-medium">{formatPrimitive(v)}</span>
                </p>
              ))}
            </div>
          )}

          {/* Nested structures */}
          {nestedRows.length > 0 && (
            <div className="space-y-2">
              {nestedRows.map(([k, v]) => (
                <div key={k}>
                  <p className="text-sm font-semibold capitalize mb-1">{k.replace(/_/g, " ")}</p>
                  <div className="pl-3 border-l border-black/10">{renderValue(v)}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }

    // Primitives
    return (
      <p className="whitespace-pre-wrap break-words text-sm text-gray-800 leading-relaxed">{formatPrimitive(value)}</p>
    )
  }

  const renderAnalyticalContent = (data: EDAResults) => {
    const analyticalData = { ...data }
    delete analyticalData.plotly_json // Remove plotly data for text analysis

    const entries = Object.entries(analyticalData)

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {entries.map(([key, value]) => (
          <div key={key} className="text-center">
            <h3 className="text-lg font-black mb-2 text-black uppercase tracking-wide">{key.replace(/_/g, " ")}</h3>
            <div className="text-gray-800 text-sm leading-relaxed text-left">{renderValue(value)}</div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div
      className="min-h-screen p-6"
      style={{
        background: `
          linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px),
          linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
          #dbeafe
        `,
        backgroundSize: "40px 40px",
      }}
    >
      {/* Header */}
      <div className="max-w-7xl mx-auto mb-8">
        <div
          className="border-4 border-black shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] p-8"
          style={{ backgroundColor: "#dbeafe" }} // Same background color as main but no checks
        >
          <h1 className="text-4xl font-black text-black mb-2 tracking-tight">DATA ANALYSIS STUDIO</h1>
          <p className="text-lg font-bold text-gray-700">Upload your CSV and get instant insights</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto">
        {/* Results Section */}
        {results && (
          <div className="mb-8">
            <div
              className="border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-8 mb-8"
              style={{ backgroundColor: "#dbeafe" }} // Same background color as main but no checks
            >
              <div className="flex items-center gap-3 mb-6">
                <TrendingUp className="w-6 h-6" />
                <h2 className="text-2xl font-black text-black uppercase">Analysis Results</h2>
              </div>

              {renderAnalyticalContent(results)}

              {results && results.plotly_json && (
                <div className="space-y-6">
                  <div className="flex items-center gap-3 mb-4">
                    <BarChart3 className="w-6 h-6" />
                    <h3 className="text-xl font-black text-black uppercase">Data Visualizations</h3>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {Array.isArray(results.plotly_json) ? (
                      results.plotly_json.map((plotData: PlotlyData, index: number) => (
                        <div
                          key={index}
                          className="bg-white border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-4"
                        >
                          <Plot
                            data={plotData.data}
                            layout={{
                              ...plotData.layout,
                              font: { family: "Arial Black, sans-serif", size: 12 },
                              paper_bgcolor: "#ffffff",
                              plot_bgcolor: "#f8fafc",
                              margin: { l: 40, r: 40, t: 40, b: 40 },
                            }}
                            config={{ responsive: true, displayModeBar: false }}
                            style={{ width: "100%", height: "400px" }}
                          />
                        </div>
                      ))
                    ) : (
                      <div className="lg:col-span-2">
                        <div className="bg-white border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-4">
                          <Plot
                            data={(results.plotly_json as PlotlyData).data}
                            layout={{
                              ...(results.plotly_json as PlotlyData).layout,
                              font: { family: "Arial Black, sans-serif", size: 14 },
                              paper_bgcolor: "#ffffff",
                              plot_bgcolor: "#f8fafc",
                            }}
                            config={{ responsive: true, displayModeBar: false }}
                            style={{ width: "100%", height: "500px" }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-200 border-4 border-red-600 shadow-[6px_6px_0px_0px_rgba(220,38,38,1)]">
            <p className="text-red-800 font-bold">{error}</p>
          </div>
        )}

        <div
          className="fixed bottom-0 left-0 right-0 border-t-4 border-black p-6 shadow-[0px_-8px_0px_0px_rgba(0,0,0,1)]"
          style={{ backgroundColor: "#dbeafe" }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center gap-4">
              {/* File Upload Icon */}
              <div className="relative">
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  disabled={loading}
                />
                <div
                  className={`p-3 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] cursor-pointer hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transition-all ${csvFile ? "bg-green-300" : "bg-white"}`}
                >
                  <Upload className="w-6 h-6" />
                </div>
              </div>

              {/* Query Input */}
              <div className="flex-1">
                <input
                  type="text"
                  value={userQuery}
                  onChange={(e) => setUserQuery(e.target.value)}
                  placeholder="What insights do you want from this data?"
                  className="w-full p-3 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] font-medium text-black placeholder-gray-500 focus:outline-none focus:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transition-all"
                  disabled={loading}
                  onKeyPress={(e) => e.key === "Enter" && handleSubmit()}
                />
              </div>

              {/* Analyze Icon */}
              <button
                onClick={handleSubmit}
                disabled={loading || !csvContent || !userQuery.trim()}
                className="p-3 bg-sky-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:bg-sky-400 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="w-6 h-6 border-2 border-black border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Bottom padding to account for fixed input */}
        <div className="h-24"></div>
      </div>
    </div>
  )
}

export default EDAPage
