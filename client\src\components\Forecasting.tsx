"use client"

import type React from "react"
import { useMemo, useState } from "react"
import { Upload, Plus, Send, X, ChevronUp, ChevronDown } from "lucide-react"

function parseCsv(text: string): string[][] {
  const rows: string[][] = []
  let current: string[] = []
  let field = ""
  let inQuotes = false

  const pushField = () => {
    current.push(field)
    field = ""
  }
  const pushRow = () => {
    rows.push(current)
    current = []
  }

  for (let i = 0; i < text.length; i++) {
    const c = text[i]
    const next = text[i + 1]

    if (c === '"') {
      if (inQuotes && next === '"') {
        field += '"'
        i++
      } else {
        inQuotes = !inQuotes
      }
    } else if (c === "," && !inQuotes) {
      pushField()
    } else if ((c === "\n" || (c === "\r" && next === "\n")) && !inQuotes) {
      if (c === "\r") i++ // consume \n after \r
      pushField()
      pushRow()
    } else {
      field += c
    }
  }
  // flush last
  pushField()
  if (current.length > 1 || current[0] !== "") {
    pushRow()
  }

  // trim surrounding whitespace for each field
  return rows.map((r) => r.map((f) => f.trim()))
}

function getHeaders(csv: string): string[] {
  const rows = parseCsv(csv)
  return rows.length > 0 ? rows[0] : []
}

export default function ForecastPage() {
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvContent, setCsvContent] = useState<string>("")
  const [headers, setHeaders] = useState<string[]>([])
  const [selectedCol, setSelectedCol] = useState<string>("")
  const [chosenCols, setChosenCols] = useState<string[]>([])
  const [allCols, setAllCols] = useState<boolean>(false)
  const [yrs, setYrs] = useState<number>(1)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string>("")
  const [resultCsv, setResultCsv] = useState<string | null>(null)

  const csvTable = useMemo(() => {
    if (!csvContent) return null
    const rows = parseCsv(csvContent)
    if (!rows.length) return null
    const [head, ...body] = rows
    return { head, body }
  }, [csvContent])

  const resultTable = useMemo(() => {
    if (!resultCsv) return null
    // Replace all possible escaped newlines with real newlines
    let cleanedCsv = resultCsv
    if (cleanedCsv.startsWith('"') && cleanedCsv.endsWith('"')) {
        cleanedCsv = JSON.parse(cleanedCsv)
    }
    cleanedCsv = cleanedCsv
        .replace(/\\r\\n/g, '\n')
        .replace(/\\n/g, '\n')
        .replace(/\\r/g, '\n')
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .trim()
    const rows = parseCsv(cleanedCsv)
    if (!rows.length) return null
    const [head, ...body] = rows
    return { head, body }
    }, [resultCsv])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0]
    if (!file || file.type !== "text/csv") {
      setError("Please upload a valid CSV file")
      return
    }
    setError("")
    setCsvFile(file)
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result ?? ""
      const text = typeof result === "string" ? result : ""
      setCsvContent(text)
      // sanitize: trim, remove empty headers, dedupe
      const raw = getHeaders(text)
      const cleaned = Array.from(new Set(raw.map((h) => h.trim()).filter((h) => h.length > 0)))
      setHeaders(cleaned)
      setChosenCols([])
      setSelectedCol("")
    }
    reader.readAsText(file)
  }

  const addSelectedColumn = () => {
    if (!selectedCol) return
    if (!chosenCols.includes(selectedCol)) {
      setChosenCols((prev) => [...prev, selectedCol])
    }
    setSelectedCol("")
  }

  const removeColumn = (col: string) => {
    setChosenCols((prev) => prev.filter((c) => c !== col))
  }

  const incYears = () => setYrs((v) => Math.min(5, v + 1))
  const decYears = () => setYrs((v) => Math.max(1, v - 1))

  const handleSubmit = async () => {
    if (!csvContent) {
      setError("Please upload a CSV file first")
      return
    }
    if (!allCols && chosenCols.length === 0) {
      setError("Please choose at least one column or switch to All Columns")
      return
    }
    setError("")
    setLoading(true)
    setResultCsv(null)
    try {
      // @ts-ignore: Next.js supports import.meta.env
      const baseUrl = import.meta.env.VITE_API_BASE_URL
      const payload = {
        csv_content: csvContent,
        cols: chosenCols,
        all_col: allCols,
        yrs,
      }
      const res = await fetch(`${baseUrl}/forecast`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      })
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}`)
      }
      const text = await res.text() // API returns raw CSV
      setResultCsv(text)
    } catch (e: any) {
      setError(`Error forecasting: ${e?.message || "Unknown error"}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div
      className="min-h-screen p-6"
      style={{
        background: `
          linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px),
          linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
          #dbeafe
        `,
        backgroundSize: "40px 40px",
      }}
    >
      {/* Header */}
      <div className="max-w-7xl mx-auto mb-8">
        <div
          className="border-4 border-black shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] p-8"
          style={{ backgroundColor: "#dbeafe" }}
        >
          <h1 className="text-4xl font-black text-black mb-2 tracking-tight">FORECASTING</h1>
          <p className="text-lg font-bold text-gray-700">Upload a CSV, choose columns, set years, and forecast</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto space-y-8">
        {/* Form - All in one horizontal line */}
        <div
          className="border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-6"
          style={{ backgroundColor: "#dbeafe" }}
        >
          <div className="flex items-center gap-4 flex-wrap">
            {/* Upload Button */}
            <div className="relative">
              <input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                disabled={loading}
              />
              <div
                className={`p-3 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] cursor-pointer hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transition-all ${csvFile ? "bg-green-300" : "bg-white"}`}
                title="Upload CSV"
              >
                <Upload className="w-6 h-6" />
              </div>
            </div>

            {/* Toggle individual vs all columns */}
            <button
              type="button"
              onClick={() => setAllCols((v) => !v)}
              className={`px-4 py-3 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] font-black transition-all ${
                allCols ? "bg-sky-300 hover:bg-sky-400" : "bg-white hover:bg-gray-100"
              }`}
              aria-pressed={allCols}
            >
              {allCols ? "All Columns" : "Individual Columns"}
            </button>

            {/* Column Selection */}
            <div className="min-w-[240px] relative">
              <select
                value={selectedCol}
                onChange={(e) => setSelectedCol(e.target.value)}
                disabled={allCols || headers.length === 0 || loading}
                className="w-full p-3 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] bg-white font-bold appearance-none cursor-pointer disabled:opacity-50"
              >
                <option value="">
                  {headers.length ? "Choose a column" : "Upload CSV to load columns"}
                </option>
                {headers
                  .map((h) => h.trim())
                  .filter((h) => h.length > 0)
                  .map((h) => (
                    <option key={h} value={h}>
                      {h}
                    </option>
                  ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" />
            </div>

            {/* Add Column Button */}
            <button
              type="button"
              onClick={addSelectedColumn}
              disabled={allCols || !selectedCol || loading}
              className="p-3 bg-sky-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:bg-sky-400 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              title="Add column"
            >
              <Plus className="w-5 h-5" />
            </button>

            {/* Years Input */}
            <div className="flex items-center gap-2">
              <label className="font-black uppercase tracking-wide text-sm">Years</label>
              <div className="flex items-stretch">
                <input
                  type="number"
                  min={1}
                  max={5}
                  step={1}
                  value={yrs}
                  onChange={(e) => {
                    const v = Number(e.target.value)
                    if (!Number.isNaN(v)) setYrs(Math.max(1, Math.min(5, v)))
                  }}
                  className="w-16 text-center p-2 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] font-bold bg-white"
                  aria-label="Forecast years"
                  disabled={loading}
                />
                <div className="flex flex-col">
                  <button
                    type="button"
                    onClick={incYears}
                    disabled={loading || yrs >= 5}
                    className="px-2 py-1 border-4 border-l-0 border-black bg-white hover:bg-gray-100 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] disabled:opacity-50"
                    aria-label="Increase years"
                  >
                    <ChevronUp className="w-3 h-3" />
                  </button>
                  <button
                    type="button"
                    onClick={decYears}
                    disabled={loading || yrs <= 1}
                    className="px-2 py-1 border-4 border-l-0 border-t-0 border-black bg-white hover:bg-gray-100 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] disabled:opacity-50"
                    aria-label="Decrease years"
                  >
                    <ChevronDown className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              onClick={handleSubmit}
              disabled={loading || !csvContent || (!allCols && chosenCols.length === 0)}
              className="p-3 bg-sky-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:bg-sky-400 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              title="Submit forecast"
            >
              {loading ? (
                <div className="w-6 h-6 border-2 border-black border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="w-6 h-6" />
              )}
            </button>
          </div>

          {/* Selected Columns Chips - Below the main row */}
          {!allCols && chosenCols.length > 0 && (
            <div className="flex items-center gap-2 flex-wrap mt-4">
              {chosenCols.map((c) => (
                <span
                  key={c}
                  className="inline-flex items-center gap-2 px-2 py-1 bg-white border-4 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] text-sm font-bold"
                >
                  {c}
                  <button
                    type="button"
                    onClick={() => removeColumn(c)}
                    aria-label={`Remove ${c}`}
                    className="p-0.5 -mr-1 hover:opacity-80"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </span>
              ))}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-200 border-4 border-red-600 shadow-[6px_6px_0px_0px_rgba(220,38,38,1)] mt-4">
              <p className="text-red-800 font-bold">{error}</p>
            </div>
          )}
        </div>

        {/* CSV Data Table */}
        {csvTable && (
          <div
            className="border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-6 overflow-auto"
            style={{ backgroundColor: "#dbeafe" }}
          >
            <h2 className="text-2xl font-black text-black uppercase mb-4">Uploaded CSV Data</h2>
            <div className="min-w-full overflow-x-auto max-h-96 overflow-y-auto">
              <table className="w-full border-collapse border-4 border-black bg-white">
                <thead>
                  <tr className="bg-gray-100">
                    {csvTable.head.map((h, idx) => (
                      <th
                        key={idx}
                        className="border-2 border-black p-3 text-left font-black whitespace-nowrap sticky top-0 bg-gray-100"
                      >
                        {h}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {csvTable.body.map((row, rIdx) => (
                    <tr key={rIdx} className={rIdx % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                      {row.map((cell, cIdx) => (
                        <td key={cIdx} className="border-2 border-black p-3 whitespace-nowrap">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Forecast Results Table */}
        {resultTable && (
          <div
            className="border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-6 overflow-auto"
            style={{ backgroundColor: "#dbeafe" }}
          >
            <h2 className="text-2xl font-black text-black uppercase mb-4">Forecast Results</h2>
            <div className="min-w-full overflow-x-auto">
              <table className="w-full border-collapse border-4 border-black bg-white">
                <thead>
                  <tr className="bg-gray-100">
                    {resultTable.head.map((h, idx) => (
                      <th
                        key={idx}
                        className="border-2 border-black p-3 text-left font-black whitespace-nowrap"
                      >
                        {h}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {resultTable.body.map((row, rIdx) => (
                    <tr key={rIdx} className={rIdx % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                      {row.map((cell, cIdx) => (
                        <td key={cIdx} className="border-2 border-black p-3 whitespace-nowrap">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}