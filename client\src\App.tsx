
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { useState } from 'react';
import './App.css';
import Chatbot from './components/Chatbot';
import MapPage from './components/MapPage';
import EDAPage from './components/EDA';
import Navbar from './components/Navbar';
import AwarenessPage from './components/Awareness';
import ForecastPage from './components/Forecasting';
import { Toaster } from './components/ui/sonner';

function App() {
  const [count, setCount] = useState(0);

  return (
    <Router>
      <Toaster />
      <Navbar forks={0} />
      <Routes>
        <Route path="/" element={
          <div className="min-h-screen bg-background p-8 pt-[100px]">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-8">
                <h1 className="text-4xl font-heading mb-4">INGRES AI Chatbot</h1>
                <p className="text-lg text-foreground/80 mb-8">
                  Intelligent Groundwater Resource Analysis System
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-main text-main-foreground p-6 rounded-base border-2 border-border shadow-shadow">
                  <h2 className="text-xl font-heading mb-3">AI-Powered Analysis</h2>
                  <ul className="space-y-2 text-sm">
                    <li>• Natural language query processing</li>
                    <li>• Automatic SQL generation</li>
                    <li>• Multi-table data analysis</li>
                    <li>• Context-aware responses</li>
                  </ul>
                </div>

                <div className="bg-secondary-background p-6 rounded-base border-2 border-border shadow-shadow">
                  <h2 className="text-xl font-heading mb-3">Multilingual Support</h2>
                  <ul className="space-y-2 text-sm">
                    <li>• Hindi, English, Tamil, Telugu</li>
                    <li>• Bengali, Gujarati & more</li>
                    <li>• Voice input/output</li>
                    <li>• Real-time translation</li>
                  </ul>
                </div>
              </div>

              <div className="text-center space-y-4">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    to="/chatbot"
                    className="inline-flex items-center justify-center rounded-base border-2 border-border bg-main text-main-foreground px-8 py-4 text-lg font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
                  >
                    Launch AI Chatbot
                  </Link>
                  <Link
                    to="/map"
                    className="inline-flex items-center justify-center rounded-base border-2 border-border bg-secondary-background text-foreground px-8 py-4 text-lg font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
                  >
                    Groundwater Map
                  </Link>
                  <Link
                    to="/awareness"
                    className="inline-flex items-center justify-center rounded-base border-2 border-border bg-secondary-background text-foreground px-8 py-4 text-lg font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
                  >
                    Water Analysis Hub
                  </Link>
                </div>
              </div>

              <div className="mt-8 text-center">
                <p className="text-sm text-foreground/60">
                  Counter: <span className="font-mono">{count}</span>
                  <button
                    onClick={() => setCount((count) => count + 1)}
                    className="ml-4 px-3 py-1 bg-main text-main-foreground rounded border-2 border-border text-xs"
                  >
                    +1
                  </button>
                </p>
              </div>
            </div>
          </div>
        } />
        <Route path="/chatbot" element={<Chatbot />} />
        <Route path="/map" element={<MapPage />} />
        <Route path="/eda" element={<EDAPage />} />
        <Route path="/forecast" element={<ForecastPage />} />
        <Route path="/awareness" element={<AwarenessPage />} />
      </Routes>
    </Router>
  );
}

export default App;
