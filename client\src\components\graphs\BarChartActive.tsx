"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, BarC<PERSON>, CartesianGrid, Rectangle, XAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface BarChartActiveProps {
  data: Array<{
    [key: string]: any;
    fill?: string;
  }>;
  config: ChartConfig;
  title?: string;
  description?: string;
  dataKey: string;
  nameKey: string;
  activeIndex?: number;
  className?: string;
}

export default function BarChartActive({
  data,
  config,
  title = "Bar Chart - Active",
  description = "Data visualization",
  dataKey,
  nameKey,
  activeIndex = 2,
  className = ""
}: BarChartActiveProps) {
  return (
    <Card className={`bg-secondary-background text-foreground ${className}`}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={config}>
          <BarChart accessibilityLayer data={data}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey={nameKey}
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) =>
                config[value as keyof typeof config]?.label || value
              }
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Bar
              dataKey={dataKey}
              strokeWidth={2}
              radius={8}
              activeIndex={activeIndex}
              activeBar={({ ...props }) => {
                return (
                  <Rectangle
                    {...props}
                    fillOpacity={0.8}
                    stroke={props.payload.fill}
                    className="!stroke-4"
                  />
                )
              }}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing data visualization results
        </div>
      </CardFooter>
    </Card>
  )
}
