import React, { useState, useEffect } from "react";
import L from "leaflet";
import { X, MessageCircle } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { Map<PERSON>ontainer, GeoJSON } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { HARDCODED_DISTRICT_CATEGORIES } from "../data/districtCategories";

// Fix for Leaflet default icons in React
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

// District data interface from CSV
interface DistrictData {
  serialNo: string;
  state: string;
  district: string;
  rainfallTotal: number;
  geoAreaRechargeTotal: number;
  geoAreaTotal: number;
  gwRechargeTotal: number;
  annualResTotal: number;
  gwExtractionTotal: number;
  soe: number;
  category: string;
}

// Color mapping for categories
const CATEGORY_COLORS: { [key: string]: string } = {
  "Safe": "#22c55e",           // Green
  "Semi-Critical": "#fbbf24",  // Yellow
  "Critical": "#f97316",       // Orange
  "Over-exploited": "#dc2626", // Red
  "no-data": "#6b7280",        // Grey
};


interface InteractiveMapProps {
  onDistrictSelect?: (district: DistrictData | null) => void;
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({ onDistrictSelect }) => {
  const [statesData, setStatesData] = useState<any>(null);
  const [districtsData, setDistrictsData] = useState<any>(null);
  const [selectedStates, setSelectedStates] = useState<Set<string>>(new Set());
  const [, setStateNameToBounds] = useState<Map<string, any>>(new Map());
  const [mapRef, setMapRef] = useState<any>(null);
  const [hoveredName, setHoveredName] = useState<string | null>(null);
  const [searchState, setSearchState] = useState<string>("");
  const [selectedDistrict, setSelectedDistrict] = useState<DistrictData | null>(null);
  const [districtDataMap, setDistrictDataMap] = useState<Map<string, DistrictData>>(new Map());

  const INITIAL_CENTER: [number, number] = [22.9734, 78.6569];
  const INITIAL_ZOOM = 5;

  useEffect(() => {
    // Load states GeoJSON (served from public/)
    fetch("/india_states.json")
      .then((res) => res.json())
      .then((data) => setStatesData(data));

    // Load districts GeoJSON (served from public/)
    fetch("/india_districts.json")
      .then((res) => res.json())
      .then((data) => setDistrictsData(data));

    // Load district data from CSV
    fetch("/CentralReport_with_category.csv")
      .then((res) => res.text())
      .then((csvText) => {
        const lines = csvText.split('\n');
        const dataMap = new Map<string, DistrictData>();
        
        // Skip header row
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line) {
            const columns = line.split(',');
            if (columns.length >= 11) {
              const districtData: DistrictData = {
                serialNo: columns[0]?.trim(),
                state: columns[1]?.trim(),
                district: columns[2]?.trim(),
                rainfallTotal: parseFloat(columns[3]) || 0,
                geoAreaRechargeTotal: parseFloat(columns[4]) || 0,
                geoAreaTotal: parseFloat(columns[5]) || 0,
                gwRechargeTotal: parseFloat(columns[6]) || 0,
                annualResTotal: parseFloat(columns[7]) || 0,
                gwExtractionTotal: parseFloat(columns[8]) || 0,
                soe: parseFloat(columns[9]) || 0,
                category: columns[10]?.trim(),
              };
              
              if (districtData.district && districtData.district !== '') {
                // Store with both exact name and lowercase for flexible matching
                dataMap.set(districtData.district, districtData);
                dataMap.set(districtData.district.toLowerCase(), districtData);
              }
            }
          }
        }
        
        setDistrictDataMap(dataMap);
        console.log("Loaded district data for", Math.floor(dataMap.size / 2), "districts");
      })
      .catch((error) => {
        console.error("Error loading district data:", error);
      });
  }, []);

  // Style for states (kept visible even when districts are shown)
  const stateStyle = {
    fillColor: "#6baed6",
    weight: 1,
    opacity: 1,
    color: "black",
    dashArray: "3",
    fillOpacity: 0.6,
  };

  // Function to get district style based on category
  const getDistrictStyle = (feature: any) => {
    const districtName = feature?.properties?.NAME_2 || feature?.properties?.DISTRICT || feature?.properties?.district || feature?.properties?.dist_name;
    
    if (!districtName) return {
      fillColor: CATEGORY_COLORS["no-data"],
      weight: 1,
      opacity: 1,
      color: "white",
      dashArray: "2",
      fillOpacity: 0.7,
    };
    
    // Try to get data from CSV first
    let districtData = districtDataMap.get(districtName) || districtDataMap.get(districtName.toLowerCase());
    let category = "no-data";
    
    if (districtData) {
      category = districtData.category;
    } else {
      // Fallback to hardcoded categories if CSV data not available
      const cleanDistrictName = districtName.trim().toLowerCase();
      const matchingKey = Object.keys(HARDCODED_DISTRICT_CATEGORIES).find(key => 
        key.trim().toLowerCase() === cleanDistrictName
      );
      category = matchingKey ? HARDCODED_DISTRICT_CATEGORIES[matchingKey] : "no-data";
    }
    
    const fillColor = CATEGORY_COLORS[category] || CATEGORY_COLORS["no-data"];
    
    return {
      fillColor,
      weight: 1,
      opacity: 1,
      color: "white",
      dashArray: "2",
      fillOpacity: 0.7,
    };
  };

  // Derived hover styles for visual feedback
  const getHoverStyle = (base: any) => ({
    ...base,
    weight: 2,
    color: "#666",
    fillOpacity: Math.min((base?.fillOpacity ?? 0.6) + 0.15, 1),
  });

  // Handle state click (single-select: show only clicked state's districts)
  const onStateClick = (event: any) => {
    const p = event.target.feature.properties || {};
    const stateName = p.NAME_1 || p.ST_NM || p.st_nm || p.state || p.State || p.state_name;
    const clickedBounds = (() => { try { return event.target.getBounds(); } catch { return null; } })();

    // Single-select: replace any previous selection
    const nextSelected = new Set<string>(stateName ? [stateName] : []);
    setSelectedStates(nextSelected);

    // Store bounds for this state
    if (stateName && clickedBounds) {
      setStateNameToBounds((prev) => {
        const m = new Map(prev);
        m.set(stateName, clickedBounds);
        return m;
      });
    }

    // Zoom to the clicked state's bounds
    try {
      const map = event.target._map;
      if (map && clickedBounds) {
        map.fitBounds(clickedBounds, { padding: [20, 20] });
      }
    } catch {}
  };

  // Handle district click
  const onDistrictClick = (event: any) => {
    const p = event.target.feature.properties || {};
    const districtName = p.NAME_2 || p.DISTRICT || p.district || p.dist_name;
    
    if (districtName) {
      // Try exact match first, then lowercase
      let districtData = districtDataMap.get(districtName) || districtDataMap.get(districtName.toLowerCase());
      
      if (districtData) {
        setSelectedDistrict(districtData);
        onDistrictSelect?.(districtData); // Notify parent component
      } else {
        // If no data found, create a basic entry
        const basicDistrictData = {
          serialNo: "",
          state: "",
          district: districtName,
          rainfallTotal: 0,
          geoAreaRechargeTotal: 0,
          geoAreaTotal: 0,
          gwRechargeTotal: 0,
          annualResTotal: 0,
          gwExtractionTotal: 0,
          soe: 0,
          category: "no-data",
        };
        setSelectedDistrict(basicDistrictData);
        onDistrictSelect?.(basicDistrictData); // Notify parent component
      }
    }
  };

  // Attach interaction to states
  const onEachState = (feature: any, layer: any) => {
    layer.on({
      click: onStateClick,
      mouseover: (e: any) => {
        e.target.setStyle(getHoverStyle(stateStyle));
        try { setHoveredName(feature?.properties?.NAME_1 || null); } catch {}
        try { e.target.bringToFront(); } catch {}
      },
      mouseout: (e: any) => {
        e.target.setStyle(stateStyle);
        setHoveredName((prev) => (prev === (feature?.properties?.NAME_1 || null) ? null : prev));
      },
    });
  };

  // Attach interaction to districts
  const onEachDistrict = (feature: any, layer: any) => {
    const districtStyle = getDistrictStyle(feature);
    layer.setStyle(districtStyle);
    
    layer.on({
      click: onDistrictClick,
      mouseover: (e: any) => {
        e.target.setStyle(getHoverStyle(districtStyle));
        try { setHoveredName(feature?.properties?.NAME_2 || null); } catch {}
        try { e.target.bringToFront(); } catch {}
      },
      mouseout: (e: any) => {
        e.target.setStyle(districtStyle);
        setHoveredName((prev) => (prev === (feature?.properties?.NAME_2 || null) ? null : prev));
      },
    });
  };

  // Filter districts for all selected states
  const getFilteredDistricts = () => {
    if (!districtsData || selectedStates.size === 0) return null;

    return {
      ...districtsData,
      features: districtsData.features.filter((f: any) => {
        const p = f.properties || {};
        const stateName = p.NAME_1 || p.ST_NM || p.st_nm || p.state || p.State || p.state_name;
        return stateName && selectedStates.has(stateName);
      }),
    };
  };

  // Force GeoJSON to remount when selection changes so features update
  const selectedStatesKey = Array.from(selectedStates).sort().join("|");

  // Build states list for search dropdown
  const stateOptions: string[] = React.useMemo(() => {
    try {
      const names = ((statesData?.features || []) as any[])
        .map((f: any) => String(f?.properties?.NAME_1))
        .filter((v: string) => Boolean(v)) as string[];
      const unique = Array.from(new Set<string>(names));
      unique.sort((a, b) => a.localeCompare(b));
      return unique;
    } catch {
      return [] as string[];
    }
  }, [statesData]);

  const handleSearchSelect = (name: string) => {
    setSearchState(name);
    if (!name || !statesData) return;
    try {
      const feature = (statesData.features || []).find((f: any) => (f?.properties?.NAME_1) === name);
      if (feature) {
        const featureLayer = L.geoJSON(feature as any);
        const bounds = featureLayer.getBounds();
        setSelectedStates(new Set([name]));
        setStateNameToBounds((prev) => {
          const m = new Map(prev);
          m.set(name, bounds);
          return m;
        });
        if (mapRef && bounds && bounds.isValid && bounds.isValid()) {
          mapRef.fitBounds(bounds, { padding: [20, 20] });
        }
      }
    } catch {}
  };

  // Relax React-Leaflet prop typing for our TS config
  const AnyMapContainer: any = MapContainer;
  const AnyGeoJSON: any = GeoJSON;

  return (
    <AnyMapContainer
      center={INITIAL_CENTER}
      zoom={INITIAL_ZOOM}
      attributionControl={false}
      style={{ height: "100%", width: "100%", background: "#f5f5f4" }}
      whenCreated={(mapInstance: any) => setMapRef(mapInstance)}
    >
      {/* Neobrutalism Controls */}
      <div style={{ position: "absolute", top: 12, left: 12, zIndex: 10000 }}>
        {/* State search - Neobrutalism Style */}
        {/* <div className="flex items-center space-x-2 bg-secondary-background rounded-base shadow-shadow border-2 border-border px-3 py-2 mb-2">
          <label className="text-xs text-foreground/60 font-base">Go to state</label>
          <select
            className="text-sm bg-transparent outline-none font-base text-foreground"
            value={searchState}
            onChange={(e) => handleSearchSelect(e.target.value)}
          >
            <option value="">Select</option>
            {stateOptions.map((s) => (
              <option key={s} value={s}>{s}</option>
            ))}
          </select>
        </div> */}
      </div>



      {/* Always show states as a base layer */}
      {statesData && (
        <AnyGeoJSON
          data={statesData as any}
          style={stateStyle}
          onEachFeature={onEachState}
        />
      )}

      {/* Neobrutalism Hover info chip */}
      {hoveredName && (
        <div style={{ position: "absolute", bottom: 16, left: 16, zIndex: 10000 }}>
          <div className="bg-secondary-background rounded-base shadow-shadow border-2 border-border px-3 py-1 text-sm font-base">
            {hoveredName}
          </div>
        </div>
      )}

      {/* Show districts of selected states */}
      {selectedStates.size > 0 && getFilteredDistricts() && (
        <AnyGeoJSON
          key={`districts-${selectedStatesKey}`}
          data={getFilteredDistricts() as any}
          onEachFeature={onEachDistrict}
        />
      )}


      {/* Neobrutalism District Data Sidebar - Only show when parent doesn't handle display */}
      {selectedDistrict && !onDistrictSelect && (
        <div className="fixed inset-0 z-[9999]" onClick={() => setSelectedDistrict(null)}>
          <div className="absolute right-0 top-0 h-full w-96 bg-secondary-background shadow-shadow border-l-2 border-border transform transition-transform duration-300 ease-in-out z-[10000]" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between p-4 border-b-2 border-border bg-background">
              <div>
                <h2 className="text-lg font-heading text-foreground">{selectedDistrict.district}</h2>
                <p className="text-sm font-base text-foreground/60">{selectedDistrict.state}</p>
              </div>
              <Button
                variant="neutral"
                size="sm"
                onClick={() => setSelectedDistrict(null)}
                className="h-10 w-10 p-0"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="h-full overflow-y-auto p-4 space-y-4 scrollbar">
              {/* Neobrutalism Status Badge */}
              <div className="flex items-center justify-center">
                <Badge
                  className="text-lg px-4 py-2 font-heading border-2 border-border rounded-base shadow-shadow"
                  style={{
                    backgroundColor: CATEGORY_COLORS[selectedDistrict.category],
                    color: '#000'
                  }}
                >
                  {selectedDistrict.category}
                </Badge>
              </div>

              {/* Neobrutalism Data Grid */}
              <div className="space-y-4">
                {/* Rainfall Data */}
                <Card className="bg-secondary-background border-2 border-border shadow-shadow">
                  <CardHeader className="pb-2 border-b-2 border-border">
                    <CardTitle className="text-sm font-heading text-main">Rainfall Data</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-heading text-foreground">{selectedDistrict.rainfallTotal.toFixed(1)}</div>
                      <div className="text-xs font-base text-foreground/60">Total Rainfall (mm)</div>
                    </div>
                  </CardContent>
                </Card>

                {/* Geographic Area */}
                <Card className="bg-secondary-background border-2 border-border shadow-shadow">
                  <CardHeader className="pb-2 border-b-2 border-border">
                    <CardTitle className="text-sm font-heading text-main">Geographic Area (sq km)</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-xs font-base text-foreground/60">Recharge Area:</span>
                      <span className="text-sm font-heading text-foreground">{selectedDistrict.geoAreaRechargeTotal.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs font-base text-foreground/60">Total Area:</span>
                      <span className="text-sm font-heading text-foreground">{selectedDistrict.geoAreaTotal.toLocaleString()}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Groundwater Resources */}
                <Card className="bg-secondary-background border-2 border-border shadow-shadow">
                  <CardHeader className="pb-2 border-b-2 border-border">
                    <CardTitle className="text-sm font-heading text-main">Groundwater Resources (MCM)</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center">
                        <div className="text-lg font-heading text-foreground">{selectedDistrict.gwRechargeTotal.toLocaleString()}</div>
                        <div className="text-xs font-base text-foreground/60">Total Recharge</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-heading text-foreground">{selectedDistrict.annualResTotal.toLocaleString()}</div>
                        <div className="text-xs font-base text-foreground/60">Annual Recharge</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-heading text-foreground">{selectedDistrict.gwExtractionTotal.toLocaleString()}</div>
                        <div className="text-xs font-base text-foreground/60">Total Extraction</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-heading text-foreground">{selectedDistrict.soe.toFixed(1)}%</div>
                        <div className="text-xs font-base text-foreground/60">Stage of Extraction</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Additional Info */}
                {selectedDistrict.serialNo && (
                  <Card className="bg-secondary-background border-2 border-border shadow-shadow">
                    <CardHeader className="pb-2 border-b-2 border-border">
                      <CardTitle className="text-sm font-heading text-main">Additional Information</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <div className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-xs font-base text-foreground/60">Serial No:</span>
                          <span className="text-sm font-heading text-foreground">{selectedDistrict.serialNo}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs font-base text-foreground/60">State:</span>
                          <span className="text-sm font-heading text-foreground">{selectedDistrict.state}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Neobrutalism Ask AI Button */}
              <div className="pt-4 border-t-2 border-border">
                <Button
                  className="w-full text-sm h-10 font-base"
                  onClick={() => {
                    // Navigate to chatbot with district-specific query
                    window.location.href = `/chatbot?q=Tell me more about groundwater conditions, trends, and recommendations for ${selectedDistrict.district} district`;
                  }}
                >
                  <MessageCircle className="mr-2 h-4 w-4" />
                  Ask AI about {selectedDistrict.district}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AnyMapContainer>
  );
};

export default InteractiveMap;
