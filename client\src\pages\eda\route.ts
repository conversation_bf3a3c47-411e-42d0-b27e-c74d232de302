import { NextResponse } from "next/server"

type Stats = { count: number; mean: number | null; std: number | null; min: number | null; max: number | null }

function parseCSV(text: string) {
  // Simple CSV parser: handles commas and trims, quotes minimal
  const lines = text.split(/\r?\n/).filter((l) => l.trim().length > 0)
  if (lines.length === 0) return { headers: [] as string[], rows: [] as string[][] }

  const splitLine = (line: string) => {
    const out: string[] = []
    let cur = ""
    let inQuotes = false
    for (let i = 0; i < line.length; i++) {
      const ch = line[i]
      if (ch === '"') {
        if (inQuotes && line[i + 1] === '"') {
          cur += '"'
          i++
        } else {
          inQuotes = !inQuotes
        }
      } else if (ch === "," && !inQuotes) {
        out.push(cur)
        cur = ""
      } else {
        cur += ch
      }
    }
    out.push(cur)
    return out.map((s) => s.trim())
  }

  const headerCandidates = splitLine(lines[0])
  // Heuristic: if first row contains any non-numeric tokens, treat as header
  const firstDataRow = lines[1] ? splitLine(lines[1]) : []
  const firstRowHasNonNumeric = headerCandidates.some((h, i) => {
    const n = Number.parseFloat(h)
    const d = firstDataRow[i]
    // If the first row token is non-numeric OR differs in shape from the second row numerically, assume header
    return !isFinite(n) || (d != null && isFinite(Number.parseFloat(d)) && !isFinite(Number.parseFloat(h)))
  })

  let headers: string[] = []
  let rows: string[][] = []
  if (firstRowHasNonNumeric) {
    headers = headerCandidates.map((h, idx) => h || `col_${idx + 1}`)
    rows = lines.slice(1).map(splitLine)
  } else {
    // No header line; synthesize
    const width = headerCandidates.length
    headers = Array.from({ length: width }, (_, i) => `col_${i + 1}`)
    rows = [headerCandidates, ...lines.slice(1).map(splitLine)]
  }

  // Normalize row widths
  rows = rows.map((r) =>
    r.length < headers.length ? [...r, ...Array(headers.length - r.length).fill("")] : r.slice(0, headers.length),
  )
  return { headers, rows }
}

function computeStats(headers: string[], rows: string[][]) {
  const numeric = new Set<string>()
  const stats: Record<string, Stats> = {}

  // Welford for mean/std
  const state = headers.map(() => ({
    n: 0,
    mean: 0,
    M2: 0,
    min: Number.POSITIVE_INFINITY,
    max: Number.NEGATIVE_INFINITY,
  }))

  for (const row of rows) {
    row.forEach((cell, idx) => {
      const v = Number.parseFloat(cell.replace(/,/g, ""))
      if (isFinite(v)) {
        numeric.add(headers[idx])
        const s = state[idx]
        s.n += 1
        const delta = v - s.mean
        s.mean += delta / s.n
        const delta2 = v - s.mean
        s.M2 += delta * delta2
        if (v < s.min) s.min = v
        if (v > s.max) s.max = v
      }
    })
  }

  headers.forEach((h, idx) => {
    const s = state[idx]
    if (s.n === 0) {
      stats[h] = { count: 0, mean: null, std: null, min: null, max: null }
    } else {
      const variance = s.n > 1 ? s.M2 / (s.n - 1) : 0
      stats[h] = {
        count: s.n,
        mean: Number(s.mean.toFixed(4)),
        std: Number(Math.sqrt(variance).toFixed(4)),
        min: Number(s.min.toFixed(4)),
        max: Number(s.max.toFixed(4)),
      }
    }
  })

  return { numericColumns: Array.from(numeric), stats }
}

function buildFigure(headers: string[], rows: string[][], numericColumns: string[]) {
  // If there are at least two numeric columns, build scatter x vs y (first two)
  if (numericColumns.length >= 2) {
    const [xCol, yCol] = numericColumns.slice(0, 2)
    const xi = headers.indexOf(xCol)
    const yi = headers.indexOf(yCol)
    const x = rows.map((r) => Number.parseFloat(r[xi])).filter((v) => isFinite(v))
    const y = rows.map((r) => Number.parseFloat(r[yi])).filter((v) => isFinite(v))
    return {
      data: [
        {
          type: "scatter",
          mode: "markers",
          x,
          y,
          marker: { color: "#2563eb" },
          name: `${xCol} vs ${yCol}`,
        },
      ],
      layout: {
        title: `${xCol} vs ${yCol}`,
        xaxis: { title: xCol },
        yaxis: { title: yCol },
        margin: { l: 48, r: 16, t: 48, b: 48 },
      },
      config: { displayModeBar: true },
    }
  }

  // Else, if there is at least one numeric column, histogram that column
  if (numericColumns.length === 1) {
    const col = numericColumns[0]
    const idx = headers.indexOf(col)
    const vals = rows.map((r) => Number.parseFloat(r[idx])).filter((v) => isFinite(v))
    return {
      data: [{ type: "histogram", x: vals, marker: { color: "#2563eb" }, name: col }],
      layout: { title: `Distribution of ${col}`, margin: { l: 48, r: 16, t: 48, b: 48 } },
      config: { displayModeBar: true },
    }
  }

  // Otherwise, no numeric figure
  return undefined
}

export async function POST(req: Request) {
  try {
    const body = await req.json().catch(() => ({}))
    const csv: string = body?.csv_content ?? ""
    const userQuery: string = body?.user_query ?? ""
    if (!csv || typeof csv !== "string") {
      return NextResponse.json({ message: "csv_content is required" }, { status: 400 })
    }

    const { headers, rows } = parseCSV(csv)
    const { numericColumns, stats } = computeStats(headers, rows)
    const figure = buildFigure(headers, rows, numericColumns)

    const sampleRows = rows.slice(0, 5).map((r) => {
      const obj: Record<string, string | number> = {}
      headers.forEach((h, i) => {
        const raw = r[i] ?? ""
        const num = Number.parseFloat((raw as string).replace(/,/g, ""))
        obj[h] = isFinite(num) && (raw as string).trim() !== "" ? num : raw
      })
      return obj
    })

    const notes: string[] = []
    if (userQuery?.trim()) notes.push(`User query noted: "${userQuery.trim()}"`)

    return NextResponse.json({
      info: {
        rows: rows.length,
        cols: headers.length,
        headers,
        numericColumns,
        sampleRows,
        stats,
        notes,
      },
      figure,
      message: "EDA complete",
    })
  } catch (err: any) {
    return NextResponse.json({ message: err?.message || "Server error" }, { status: 500 })
  }
}
