"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface BarChartStackedProps {
  data: Array<{
    [key: string]: any;
  }>;
  config: ChartConfig;
  title?: string;
  description?: string;
  xAxisKey: string;
  stackedKeys: string[];
  className?: string;
}

export default function BarChartStacked({
  data,
  config,
  title = "Bar Chart - Stacked + Legend",
  description = "Data visualization",
  xAxisKey,
  stackedKeys,
  className = ""
}: BarChartStackedProps) {
  return (
    <Card className={`bg-secondary-background text-foreground ${className}`}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={config}>
          <BarChart accessibilityLayer data={data}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey={xAxisKey}
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => 
                typeof value === 'string' && value.length > 3 
                  ? value.slice(0, 3) 
                  : value
              }
            />
            <ChartTooltip
              cursor={{ fill: "#8080804D" }}
              content={<ChartTooltipContent hideLabel />}
            />
            <ChartLegend content={<ChartLegendContent />} />
            {stackedKeys.map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                stackId="a"
                fill={`var(--color-${key})`}
                radius={
                  index === 0 
                    ? [0, 0, 4, 4] 
                    : index === stackedKeys.length - 1 
                    ? [4, 4, 0, 0] 
                    : [0, 0, 0, 0]
                }
              />
            ))}
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing data visualization results
        </div>
      </CardFooter>
    </Card>
  )
}
