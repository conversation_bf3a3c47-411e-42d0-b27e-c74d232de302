import { MapP<PERSON>, <PERSON>Lef<PERSON> } from 'lucide-react'
import { Link } from 'react-router-dom'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

const MapPage = () => {
  return (
    <div className="min-h-screen bg-[#FFDB58]/10">
      <Navbar forks={0} />
      
      <main className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 py-8 max-w-7xl">
        {/* Back Button */}
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="inline-block neo-border bg-[#FFDB58] px-4 py-2 font-bold text-sm mb-4">
            Interactive Map
          </div>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6">
            Explore Our <span className="text-yellow-500">Map</span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-700 max-w-3xl mx-auto">
            Discover locations, visualize data, and explore interactive features on our comprehensive map interface.
          </p>
        </div>

        {/* Map Container */}
        <div className="neo-border bg-white p-8 mb-12">
          <div className="flex items-center justify-center gap-3 mb-6">
            <MapPin className="w-6 h-6 text-yellow-500" />
            <h2 className="text-2xl font-bold">Interactive Map</h2>
          </div>
          
          {/* Placeholder for Map Component */}
          <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
            <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">Map Coming Soon</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              This is where your interactive map component will be integrated. 
              You can add your preferred mapping library (like Leaflet, Mapbox, or Google Maps) here.
            </p>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="neo-border bg-white p-6 text-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-6 h-6 text-yellow-600" />
            </div>
            <h3 className="text-xl font-bold mb-3">Location Tracking</h3>
            <p className="text-gray-600">
              Track and visualize locations with precision and real-time updates.
            </p>
          </div>
          
          <div className="neo-border bg-white p-6 text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-xl font-bold mb-3">Data Visualization</h3>
            <p className="text-gray-600">
              Display data points, trends, and analytics directly on the map interface.
            </p>
          </div>
          
          <div className="neo-border bg-white p-6 text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-3">Interactive Features</h3>
            <p className="text-gray-600">
              Zoom, pan, and interact with map elements for detailed exploration.
            </p>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}

export default MapPage
