// API utility functions for chatbot backend integration

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

export interface NewSessionResponse {
  session_id: string;
}

export interface ChatRequest {
  question?: string;
  audio_data?: string;
  input_type: 'text' | 'voice';
  session_id: string;
  include_visualization: boolean;
}

export interface ChatResponse {
  success: boolean;
  session_id: string;
  sql_query: string;
  response: string;
  translated_response?: string;
  explanation: string;
  data: Array<Record<string, any>>;
  csv_data: string;
  error: string;
  visualization: any;
  audio_response?: string;
  detected_language?: string;
  input_type: 'text' | 'voice';
  metadata: {
    rows_returned: number;
    columns: string[];
    execution_time: string;
    has_visualization: boolean;
    session_id: string;
    context_used: boolean;
    context_queries_count: number;
    mentioned_entities_count: number;
  };
  chat_history: any[];
}

export interface CSVData {
  csv_content: string;
  user_query?: string;
  response_text?: string;
}

export interface GraphConfig {
  index: number;
  title: string;
  description: string;
  x_axis_label: string;
  y_axis_label: string;
  data_keys: {
    primary_key: string;
    value_keys: string[];
    color_key?: string;
  };
  chart_specific: Record<string, any>;
}

export interface DecideGraphResponse {
  number_of_appropriate_graphs: number;
  graph_indices: number[];
  graph_configs: GraphConfig[];
  data?: Array<Record<string, any>>;
}

/**
 * Creates a new chat session
 */
export async function createNewSession(): Promise<NewSessionResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/new-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ New session created:', data);
    return data;
  } catch (error) {
    console.error('Error creating new session:', error);
    throw error;
  }
}

/**
 * Sends a chat message to the backend
 */
export async function sendChatMessage(request: ChatRequest): Promise<ChatResponse> {
  try {
    console.log('🚀 Sending chat message:', request);

    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Chat response received:', data);
    return data;
  } catch (error) {
    console.error('❌ Error sending chat message:', error);
    throw error;
  }
}

/**
 * Decides appropriate graph types based on CSV data and user query
 */
export async function decideGraphFromData(csvData: CSVData): Promise<DecideGraphResponse> {
  try {
    console.log('📊 Analyzing data for graph recommendations:', {
      csvLength: csvData.csv_content.length,
      hasUserQuery: !!csvData.user_query,
      hasResponseText: !!csvData.response_text
    });

    const response = await fetch(`${API_BASE_URL}/decide`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(csvData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Graph recommendation received:', data);
    return data;
  } catch (error) {
    console.error('❌ Error getting graph recommendations:', error);
    throw error;
  }
}


