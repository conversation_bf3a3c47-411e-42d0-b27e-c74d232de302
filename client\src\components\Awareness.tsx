import { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Download,
  BarChart3,
  Pie<PERSON><PERSON>,
  MessageCircle
} from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "./ui/accordion";
import { useToast } from "../hooks/use-toast";
import jsPDF from 'jspdf';
import WaterRequirementChart from "./charts/WaterRequirementChart";
import CropSuitabilityChart from "./charts/CropSuitabilityChart";

const AwarenessPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedState, setSelectedState] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [selectedUnit, setSelectedUnit] = useState("");
  const [season, setSeason] = useState("");
  const [soilType, setSoilType] = useState("");
  const [irrigationType, setIrrigationType] = useState("");
  const [showWaterChart, setShowWaterChart] = useState(false);
  const [showCropChart, setShowCropChart] = useState(false);

  // Function to generate and download PDF report
  const generateReport = async () => {
    if (!selectedState) {
      toast({
        title: "State Required",
        description: "Please select a state to generate the report",
        variant: "destructive",
      });
      return;
    }

    try {
      // Try to fetch groundwater data for the selected state
      let groundwaterData = null;
      try {
        const response = await fetch('/CentralReport_with_category.csv');
        const csvText = await response.text();
        const lines = csvText.split('\n');
        
        // Find data for the selected state
        const stateData = lines.find(line => 
          line.toLowerCase().includes(selectedState.toLowerCase())
        );
        
        if (stateData) {
          const values = stateData.split(',');
          groundwaterData = {
            rainfall: values[3] || 'N/A',
            geoAreaRecharge: values[4] || 'N/A',
            geoAreaTotal: values[5] || 'N/A',
            gwRecharge: values[6] || 'N/A',
            annualRes: values[7] || 'N/A',
            gwExtraction: values[8] || 'N/A',
            soe: values[9] || 'N/A',
            category: values[10] || 'N/A'
          };
        }
      } catch (error) {
        console.log("Could not fetch groundwater data:", error);
      }

      // Create PDF document
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      let yPosition = 20;

      // Helper function to add text with line breaks
      const addText = (text: string, x: number, y: number, options: any = {}) => {
        const lines = pdf.splitTextToSize(text, pageWidth - 40);
        pdf.text(lines, x, y);
        return y + (lines.length * (options.lineHeight || 7));
      };

      // Helper function to add a new page if needed
      const checkNewPage = (currentY: number) => {
        if (currentY > pageHeight - 20) {
          pdf.addPage();
          return 20;
        }
        return currentY;
      };

      // Header
      pdf.setFillColor(34, 197, 94); // Green color
      pdf.rect(0, 0, pageWidth, 30, 'F');
      
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      pdf.text('GROUNDWATER AWARENESS REPORT', pageWidth / 2, 15, { align: 'center' });
      
      pdf.setFontSize(12);
      pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth / 2, 22, { align: 'center' });

      yPosition = 40;
      pdf.setTextColor(0, 0, 0);

      // Executive Summary
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('EXECUTIVE SUMMARY', 20, yPosition);
      yPosition += 5;

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      const summaryText = `This report provides a comprehensive analysis of groundwater conditions and recommendations for ${selectedState}. The analysis includes water requirement assessments, crop suitability analysis, and sustainable groundwater management recommendations based on current data and best practices.`;
      yPosition = addText(summaryText, 20, yPosition, { lineHeight: 6 });
      yPosition += 10;

      // User Selections Section
      yPosition = checkNewPage(yPosition);
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('USER SELECTIONS', 20, yPosition);
      yPosition += 5;

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      
      const selections = [
        ['State', selectedState],
        ['District', selectedDistrict || 'Not specified'],
        ['Assessment Unit', selectedUnit || 'Not specified'],
        ['Cropping Season', season || 'Not specified'],
        ['Soil Type', soilType || 'Not specified'],
        ['Irrigation Type', irrigationType || 'Not specified'],
        ['Water Requirement Analysis', showWaterChart ? 'Calculated' : 'Not calculated'],
        ['Crop Suitability Analysis', showCropChart ? 'Analyzed' : 'Not analyzed']
      ];

      selections.forEach(([key, value]) => {
        pdf.setFont('helvetica', 'bold');
        pdf.text(`${key}:`, 20, yPosition);
        pdf.setFont('helvetica', 'normal');
        pdf.text(value, 80, yPosition);
        yPosition += 6;
      });

      yPosition += 10;

      // Groundwater Data Section
      if (groundwaterData) {
        yPosition = checkNewPage(yPosition);
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'bold');
        yPosition = addText('GROUNDWATER DATA FOR SELECTED STATE', 20, yPosition);
        yPosition += 5;

        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        
        const dataEntries = [
          ['Rainfall Total', groundwaterData.rainfall],
          ['Geological Area Recharge', groundwaterData.geoAreaRecharge],
          ['Geological Area Total', groundwaterData.geoAreaTotal],
          ['GW Recharge Total', groundwaterData.gwRecharge],
          ['Annual Reserve Total', groundwaterData.annualRes],
          ['GW Extraction Total', groundwaterData.gwExtraction],
          ['Stage of Extraction (SOE)', groundwaterData.soe],
          ['Category', groundwaterData.category]
        ];

        dataEntries.forEach(([key, value]) => {
          pdf.setFont('helvetica', 'bold');
          pdf.text(`${key}:`, 20, yPosition);
          pdf.setFont('helvetica', 'normal');
          pdf.text(value, 80, yPosition);
          yPosition += 6;
        });

        yPosition += 10;
      }

      // Recommendations Section
      yPosition = checkNewPage(yPosition);
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      yPosition = addText('RECOMMENDATIONS', 20, yPosition);
      yPosition += 5;

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      
      const recommendations = [
        'Implement efficient irrigation practices based on soil type and season',
        'Monitor groundwater levels regularly using available data',
        'Use water-saving technologies like drip irrigation',
        'Follow sustainable groundwater extraction practices',
        'Consider groundwater recharge techniques'
      ];

      recommendations.forEach((rec, index) => {
        pdf.setFont('helvetica', 'bold');
        pdf.text(`${index + 1}.`, 20, yPosition);
        pdf.setFont('helvetica', 'normal');
        yPosition = addText(rec, 30, yPosition, { lineHeight: 6 });
        yPosition += 2;
      });

      yPosition += 10;

      // Footer
      yPosition = checkNewPage(yPosition);
      pdf.setFontSize(8);
      pdf.setTextColor(128, 128, 128);
      pdf.text('This report was generated by the Groundwater Awareness Platform', pageWidth / 2, yPosition, { align: 'center' });
      pdf.text('For more detailed analysis, visit the interactive maps and charts on this platform.', pageWidth / 2, yPosition + 5, { align: 'center' });

      // Download the PDF
      const fileName = `groundwater_report_${selectedState.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);
      
      // Show success message
      toast({
        title: "PDF Report Downloaded",
        description: "Your professional groundwater report has been downloaded successfully!",
        variant: "default",
      });
    } catch (error) {
      console.error("Error generating PDF report:", error);
      toast({
        title: "PDF Generation Failed",
        description: "Error generating PDF report. Please try again.",
        variant: "destructive",
      });
    }
  };

  const educationalCards = [
    {
      image: "/groundwater_recharge.png",
      title: "Groundwater Recharge",
      description: "Natural process where water moves from surface to underground aquifers",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      image: "/sustainable_extraction.png",
      title: "Sustainable Extraction",
      description: "Balanced withdrawal that maintains long-term aquifer health",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      image: "/aquifer_management.png",
      title: "Aquifer Management",
      description: "Strategic planning for groundwater conservation and usage",
      gradient: "from-purple-500 to-indigo-500"
    },
    {
      image: "/smart_irrigation.png",
      title: "Smart Irrigation",
      description: "Technology-driven water-efficient farming practices",
      gradient: "from-orange-500 to-red-500"
    }
  ];

  const faqs = [
    {
      question: "What is groundwater over-exploitation?",
      answer: "Groundwater over-exploitation occurs when the rate of extraction exceeds the natural recharge rate, leading to declining water tables and potential aquifer depletion."
    },
    {
      question: "How does climate change affect groundwater?",
      answer: "Climate change impacts groundwater through altered precipitation patterns, increased evaporation rates, and changes in recharge patterns, potentially reducing overall groundwater availability."
    },
    {
      question: "What are the best practices for groundwater conservation?",
      answer: "Key practices include rainwater harvesting, efficient irrigation systems, groundwater recharge techniques, crop rotation, and monitoring water usage patterns."
    },
    {
      question: "How can farmers optimize water usage for crops?",
      answer: "Farmers can use drip irrigation, select drought-resistant crops, implement precision agriculture, monitor soil moisture, and follow seasonal cropping patterns based on water availability."
    }
  ];

  const states = ["Andhra Pradesh", "Gujarat", "Haryana", "Karnataka", "Maharashtra", "Punjab", "Rajasthan", "Tamil Nadu", "Uttar Pradesh", "West Bengal"];
  const districts = selectedState ? ["District 1", "District 2", "District 3", "District 4", "District 5"] : [];
  const assessmentUnits = selectedDistrict ? ["Unit A", "Unit B", "Unit C", "Unit D"] : [];

  return (
    <div className="min-h-screen bg-background p-8 pt-[100px]">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-heading mb-4">Groundwater Awareness</h1>
          <p className="text-lg text-foreground/80 mb-8">
            Learn about sustainable groundwater practices and assess your land's water requirements
          </p>
        </motion.div>

        {/* Educational Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {educationalCards.map((card, index) => (
            <div key={index} className="bg-main text-main-foreground p-6 rounded-base border-2 border-border shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 p-3 rounded-base bg-secondary-background border-2 border-border flex items-center justify-center">
                  <img src={card.image} alt={card.title} className="h-10 w-10 object-contain" />
                </div>
                <h3 className="text-lg font-heading mb-3">{card.title}</h3>
                <p className="text-sm leading-relaxed">{card.description}</p>
              </div>
            </div>
          ))}
        </motion.div>

        {/* Land & Crops Assessment Wizard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-secondary-background p-6 rounded-base border-2 border-border shadow-shadow mb-8"
        >
          <div className="text-center mb-6">
            <h2 className="text-2xl font-heading mb-3">Know Your Land & Crops</h2>
            <p className="text-sm text-foreground/80">
              Get personalized water requirement and crop suitability analysis
            </p>
          </div>
          <div className="space-y-6">
            {/* Location Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-heading mb-4">Location Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-base mb-2">State</label>
                  <Select value={selectedState} onValueChange={setSelectedState}>
                    <SelectTrigger className="h-12 border-2 border-border focus:border-ring rounded-base bg-secondary-background">
                      <SelectValue placeholder="Select State" />
                    </SelectTrigger>
                    <SelectContent className="bg-secondary-background border-2 border-border rounded-base shadow-shadow">
                      {states.map((state) => (
                        <SelectItem key={state} value={state} className="hover:bg-main hover:text-main-foreground">{state}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-base mb-2">District</label>
                  <Select value={selectedDistrict} onValueChange={setSelectedDistrict} disabled={!selectedState}>
                    <SelectTrigger className="h-12 border-2 border-border focus:border-ring rounded-base bg-secondary-background">
                      <SelectValue placeholder="Select District" />
                    </SelectTrigger>
                    <SelectContent className="bg-secondary-background border-2 border-border rounded-base shadow-shadow">
                      {districts.map((district) => (
                        <SelectItem key={district} value={district} className="hover:bg-main hover:text-main-foreground">{district}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-base mb-2">Assessment Unit</label>
                  <Select value={selectedUnit} onValueChange={setSelectedUnit} disabled={!selectedDistrict}>
                    <SelectTrigger className="h-12 border-2 border-border focus:border-ring rounded-base bg-secondary-background">
                      <SelectValue placeholder="Select Unit" />
                    </SelectTrigger>
                    <SelectContent className="bg-secondary-background border-2 border-border rounded-base shadow-shadow">
                      {assessmentUnits.map((unit) => (
                        <SelectItem key={unit} value={unit} className="hover:bg-main hover:text-main-foreground">{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Farming Parameters */}
            <div className="space-y-4">
              <h3 className="text-lg font-heading mb-4">Farming Parameters</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-base mb-2">Cropping Season</label>
                  <Select value={season} onValueChange={setSeason}>
                    <SelectTrigger className="h-12 border-2 border-border focus:border-ring rounded-base bg-secondary-background">
                      <SelectValue placeholder="Select Season" />
                    </SelectTrigger>
                    <SelectContent className="bg-secondary-background border-2 border-border rounded-base shadow-shadow">
                      <SelectItem value="kharif" className="hover:bg-main hover:text-main-foreground">Kharif (Monsoon)</SelectItem>
                      <SelectItem value="rabi" className="hover:bg-main hover:text-main-foreground">Rabi (Winter)</SelectItem>
                      <SelectItem value="summer" className="hover:bg-main hover:text-main-foreground">Summer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-base mb-2">Soil Type</label>
                  <Select value={soilType} onValueChange={setSoilType}>
                    <SelectTrigger className="h-12 border-2 border-border focus:border-ring rounded-base bg-secondary-background">
                      <SelectValue placeholder="Select Soil Type" />
                    </SelectTrigger>
                    <SelectContent className="bg-secondary-background border-2 border-border rounded-base shadow-shadow">
                      <SelectItem value="alluvial" className="hover:bg-main hover:text-main-foreground">Alluvial</SelectItem>
                      <SelectItem value="black" className="hover:bg-main hover:text-main-foreground">Black Cotton</SelectItem>
                      <SelectItem value="red" className="hover:bg-main hover:text-main-foreground">Red Soil</SelectItem>
                      <SelectItem value="laterite" className="hover:bg-main hover:text-main-foreground">Laterite</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-base mb-2">Irrigation Type</label>
                  <Select value={irrigationType} onValueChange={setIrrigationType}>
                    <SelectTrigger className="h-12 border-2 border-border focus:border-ring rounded-base bg-secondary-background">
                      <SelectValue placeholder="Select Irrigation" />
                    </SelectTrigger>
                    <SelectContent className="bg-secondary-background border-2 border-border rounded-base shadow-shadow">
                      <SelectItem value="drip" className="hover:bg-main hover:text-main-foreground">Drip Irrigation</SelectItem>
                      <SelectItem value="sprinkler" className="hover:bg-main hover:text-main-foreground">Sprinkler</SelectItem>
                      <SelectItem value="flood" className="hover:bg-main hover:text-main-foreground">Flood Irrigation</SelectItem>
                      <SelectItem value="rainfed" className="hover:bg-main hover:text-main-foreground">Rainfed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-heading mb-4">Analysis Tools</h3>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={() => {
                    if (selectedState && season) {
                      setShowWaterChart(true);
                      toast({
                        title: "Water Analysis Started",
                        description: "Calculating water requirements for your selected parameters...",
                      });
                    }
                  }}
                  disabled={!selectedState || !season}
                  className="inline-flex items-center justify-center rounded-base border-2 border-border bg-main text-main-foreground px-6 py-3 text-sm font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Estimate Water Requirement
                </Button>

                <Button
                  onClick={() => {
                    if (selectedState && soilType) {
                      setShowCropChart(true);
                      toast({
                        title: "Crop Analysis Started",
                        description: "Analyzing crop suitability for your soil type...",
                      });
                    }
                  }}
                  disabled={!selectedState || !soilType}
                  className="inline-flex items-center justify-center rounded-base border-2 border-border bg-secondary-background text-foreground px-6 py-3 text-sm font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <PieChart className="mr-2 h-4 w-4" />
                  Show Crop Suitability
                </Button>

                <Button
                  disabled={!selectedState}
                  onClick={() => {
                    if (selectedState) {
                      generateReport();
                    }
                  }}
                  className="inline-flex items-center justify-center rounded-base border-2 border-border bg-secondary-background text-foreground px-6 py-3 text-sm font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download Report
                </Button>
              </div>
            </div>

            {/* Charts */}
            {showWaterChart && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                className="mt-6 p-6 bg-main text-main-foreground rounded-base border-2 border-border shadow-shadow"
              >
                <h4 className="text-lg font-heading mb-4">Water Requirement Analysis</h4>
                <div className="space-y-4">
                  {/* Water Usage Chart */}
                  <div className="bg-secondary-background p-4 rounded-base border-2 border-border">
                    <h5 className="font-heading mb-3 text-foreground">Monthly Water Requirements (mm)</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">January</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '15%' }}></div>
                          </div>
                          <span className="text-sm font-mono">15mm</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">February</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '25%' }}></div>
                          </div>
                          <span className="text-sm font-mono">25mm</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">March</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '45%' }}></div>
                          </div>
                          <span className="text-sm font-mono">45mm</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">April</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '80%' }}></div>
                          </div>
                          <span className="text-sm font-mono">80mm</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">May</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '100%' }}></div>
                          </div>
                          <span className="text-sm font-mono">120mm</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">June</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '90%' }}></div>
                          </div>
                          <span className="text-sm font-mono">110mm</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Water Sources */}
                  <div className="bg-secondary-background p-4 rounded-base border-2 border-border">
                    <h5 className="font-heading mb-3 text-foreground">Water Sources Distribution</h5>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div className="p-3 bg-background rounded-base border-2 border-border">
                        <div className="text-2xl font-heading text-foreground">65%</div>
                        <div className="text-xs">Groundwater</div>
                      </div>
                      <div className="p-3 bg-background rounded-base border-2 border-border">
                        <div className="text-2xl font-heading text-foreground">35%</div>
                        <div className="text-xs">Rainfall</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {showCropChart && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                className="mt-6 p-6 bg-secondary-background rounded-base border-2 border-border shadow-shadow"
              >
                <h4 className="text-lg font-heading mb-4">Crop Suitability Analysis</h4>
                <div className="space-y-4">
                  {/* Crop Suitability Chart */}
                  <div className="bg-background p-4 rounded-base border-2 border-border">
                    <h5 className="font-heading mb-3 text-foreground">Crop Suitability Score</h5>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Rice</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-secondary-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '95%' }}></div>
                          </div>
                          <span className="text-sm font-mono">95%</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Wheat</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-secondary-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '85%' }}></div>
                          </div>
                          <span className="text-sm font-mono">85%</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Maize</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-secondary-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '75%' }}></div>
                          </div>
                          <span className="text-sm font-mono">75%</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Sugarcane</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-secondary-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '90%' }}></div>
                          </div>
                          <span className="text-sm font-mono">90%</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Cotton</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 h-4 bg-secondary-background border-2 border-border rounded">
                            <div className="h-full bg-main" style={{ width: '60%' }}></div>
                          </div>
                          <span className="text-sm font-mono">60%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Soil Analysis */}
                  <div className="bg-background p-4 rounded-base border-2 border-border">
                    <h5 className="font-heading mb-3 text-foreground">Soil Analysis Results</h5>
                    <div className="grid grid-cols-2 gap-3 text-center">
                      <div className="p-2 bg-secondary-background rounded-base border-2 border-border">
                        <div className="text-lg font-heading text-foreground">7.2</div>
                        <div className="text-xs">pH Level</div>
                      </div>
                      <div className="p-2 bg-secondary-background rounded-base border-2 border-border">
                        <div className="text-lg font-heading text-foreground">2.1%</div>
                        <div className="text-xs">Organic Matter</div>
                      </div>
                      <div className="p-2 bg-secondary-background rounded-base border-2 border-border">
                        <div className="text-lg font-heading text-foreground">45%</div>
                        <div className="text-xs">Clay Content</div>
                      </div>
                      <div className="p-2 bg-secondary-background rounded-base border-2 border-border">
                        <div className="text-lg font-heading text-foreground">Good</div>
                        <div className="text-xs">Drainage</div>
                      </div>
                    </div>
                  </div>

                  {/* Recommendations */}
                  <div className="bg-background p-4 rounded-base border-2 border-border">
                    <h5 className="font-heading mb-3 text-foreground">Recommendations</h5>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <span className="mr-2 text-main">•</span>
                        <span>Rice and Sugarcane are highly suitable for your soil type</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2 text-main">•</span>
                        <span>Consider crop rotation to maintain soil health</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2 text-main">•</span>
                        <span>Implement drip irrigation for water efficiency</span>
                      </li>
                      <li className="flex items-start">
                        <span className="mr-2 text-main">•</span>
                        <span>Add organic matter to improve soil structure</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-secondary-background p-6 rounded-base border-2 border-border shadow-shadow"
        >
          <div className="text-center mb-6">
            <h2 className="text-2xl font-heading mb-3">Frequently Asked Questions</h2>
          </div>
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="border-2 border-border rounded-base px-4 py-3 bg-background hover:bg-main hover:text-main-foreground transition-all duration-200">
                <AccordionTrigger className="text-left font-heading hover:text-main-foreground transition-colors duration-200">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="pt-4 pb-3">
                  <p className="text-sm leading-relaxed mb-4">{faq.answer}</p>
                  <Button
                    onClick={() => navigate(`/chatbot?q=${encodeURIComponent(faq.question)}`)}
                    className="inline-flex items-center justify-center rounded-base border-2 border-border bg-main text-main-foreground px-4 py-2 text-xs font-heading shadow-shadow transition-all hover:translate-x-1 hover:translate-y-1 hover:shadow-none"
                  >
                    <MessageCircle className="mr-2 h-3 w-3" />
                    Ask AI
                  </Button>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </div>
  );
};

export default AwarenessPage;