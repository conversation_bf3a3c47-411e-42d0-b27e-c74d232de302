import { Boxes, Github, Menu, X, MapPin } from 'lucide-react'
import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'

const Navbar = ({ forks }: { forks: number }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()
  const isLegalPage = location.pathname === '/privacy' || location.pathname === '/terms'

  // Helper function to determine if a link is active
  const isActiveLink = (path: string) => location.pathname === path

  // Helper function to get link classes based on active state
  const getLinkClasses = (path: string, baseClasses: string = "font-medium transition-colors") => {
    return isActiveLink(path)
      ? `${baseClasses} text-orange-600 hover:text-orange-800`
      : `${baseClasses} hover:text-gray-600`
  }


  const formatForkCount = (count: number): string => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`
    }
    return count.toString()
  }


  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <>
      <nav className="py-4 px-6 md:px-8 lg:px-12 border-b-4 border-black bg-white fixed top-0 left-0 right-0 z-[100] shadow-md">
        <div className="container mx-auto max-w-7xl">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-2">
              <Boxes className="w-8 h-8" />
              <span className="text-2xl font-bold">Jal Drishti</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-6">
              {isLegalPage ? (
                // Show only Home link on Privacy/Terms pages
                <Link to="/" className={getLinkClasses("http://localhost:5174/")}>Home</Link>
              ) : (
                // Show full navigation on other pages
                <>
                  <Link to="http://localhost:5174/" className={getLinkClasses("http://localhost:5174/")}>Home</Link>
                  <Link to="/awareness" className={getLinkClasses("/awareness")}>Awareness</Link>
                  <Link to="/map" className={getLinkClasses("/map")}>Map</Link>
                  <Link to="/chatbot" className={getLinkClasses("/chatbot")}>Chatbot</Link>
                  <Link to="/eda" className={getLinkClasses("/eda")}>Analysis</Link>
                  <Link to="/forecast" className={getLinkClasses("/forecast")}>Forecast</Link>
                </>
              )}


            </div>

            {/* Mobile Menu Button */}
            <button 
              className="md:hidden p-2 neo-border bg-white"
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden mt-4 py-4 border-t border-gray-200">
              <div className="flex flex-col gap-4">
                {isLegalPage ? (
                  // Show only Home link on Privacy/Terms pages
                  <Link to="/" onClick={() => setIsMenuOpen(false)} className={getLinkClasses("/", "font-medium transition-colors py-2")}>Home</Link>
                ) : (
                  // Show full navigation on other pages
                  <>
                    <Link to="/" onClick={() => setIsMenuOpen(false)} className={getLinkClasses("/", "font-medium transition-colors py-2")}>Home</Link>
                    <Link to="/awareness" onClick={() => setIsMenuOpen(false)} className={getLinkClasses("/awareness", "font-medium transition-colors py-2")}>Awareness</Link>
                    <Link to="/map" onClick={() => setIsMenuOpen(false)} className={getLinkClasses("/map", "font-medium transition-colors py-2 flex items-center gap-1")}>
                      <MapPin className="w-4 h-4" />
                      Map
                    </Link>
                    <Link to="/chatbot" onClick={() => setIsMenuOpen(false)} className={getLinkClasses("/chatbot", "font-medium transition-colors py-2")}>Chatbot</Link>
                    <Link to="/eda" onClick={() => setIsMenuOpen(false)} className={getLinkClasses("/eda", "font-medium transition-colors py-2")}>EDA</Link>
                  </>
                )}

                <div className="flex flex-col gap-3 mt-2">
                  {/* Product Hunt Button */}
                  <a href="https://www.producthunt.com/posts/neobase-2?embed=true&utm_source=badge-featured&utm_medium=badge&utm_souce=badge-neobase&#0045;2" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=936307&theme=light&t=1741073867985" alt="NeoBase - AI&#0032;powered&#0032;database&#0032;assistant | Product Hunt" style={{ height: '48px'}}  height="48" /></a>

                  {/* Github Fork Button */}
                  <a
                    href="https://github.com/bhaskarblur/neobase-ai-dba"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="neo-button flex items-center justify-center gap-2 py-2 bg-black text-white"
                  >
                    <Github className="w-4 h-4" />
                    <span>Fork Us</span>
                    <span className="bg-white/20 px-2 py-0.5 rounded-full text-xs font-mono">
                      {formatForkCount(forks || 1)}
                    </span>
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>
      
      {/* Fundraising Banner */}

      
      {/* Spacer to prevent content from being hidden under the fixed navbar and banner */}
      <div className="h-[50px]"></div>
    </>
  )
}

export default Navbar 