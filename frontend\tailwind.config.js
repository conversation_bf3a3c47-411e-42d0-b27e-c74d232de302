/** @type {import('tailwindcss').Config} */
export default {
  mode: "jit",
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        main: 'var(--color-main)',
        background: 'var(--color-background)',
        'secondary-background': 'var(--color-secondary-background)',
        foreground: 'var(--color-foreground)',
        'main-foreground': 'var(--color-main-foreground)',
        border: 'var(--color-border)',
        ring: 'var(--color-ring)',
        overlay: 'var(--color-overlay)',
        chart: {
          1: 'var(--color-chart-1)',
          2: 'var(--color-chart-2)',
          3: 'var(--color-chart-3)',
          4: 'var(--color-chart-4)',
          5: 'var(--color-chart-5)',
        },
        primary: {
          yellow: '#FFDB58',
        },
        neo: {
          gray: '#F0F0F0',
          error: '#FF6B6B',
          success: '#90EE90'
        }
      },
      spacing: {
        'boxShadowX': 'var(--spacing-boxShadowX)',
        'boxShadowY': 'var(--spacing-boxShadowY)',
        'reverseBoxShadowX': 'var(--spacing-reverseBoxShadowX)',
        'reverseBoxShadowY': 'var(--spacing-reverseBoxShadowY)',
      },
      borderRadius: {
        'base': 'var(--radius-base)',
      },
      fontWeight: {
        'base': 'var(--font-weight-base)',
        'heading': 'var(--font-weight-heading)',
      },
      boxShadow: {
        'shadow': 'var(--shadow-shadow)',
      },
      fontFamily: {
        sans: ['Archivo', 'sans-serif'],
      },
    },
  },
  plugins: [],
};
