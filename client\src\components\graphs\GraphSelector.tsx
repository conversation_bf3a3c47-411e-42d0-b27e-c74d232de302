"use client"
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>C<PERSON><PERSON>, Line<PERSON><PERSON>, PieChart, BarChart4, TrendingUp } from 'lucide-react'
import <PERSON> from 'papaparse'
import BarChartActive from './BarChartActive'
import BarChartStacked from './BarChartStacked'
import LineChartDots from './LineChartDots'
import LineChartMultiple from './LineChartMultiple'
import Pie<PERSON>hartDonut from './PieChartDonut'
import { type ChartConfig } from '@/components/ui/chart'

interface GraphSelectorProps {
  csvContent: string
  userQuery?: string
  responseText?: string
  precomputedGraphDecision?: DecisionResponse // For loading from localStorage
  onGraphDecisionReady?: (decision: DecisionResponse) => void // Callback for new decisions
}

interface GraphConfig {
  index: number
  title: string
  description: string
  x_axis_label: string
  y_axis_label: string
  data_keys: {
    primary_key: string
    value_keys: string[]
    color_key?: string
  }
  chart_specific: Record<string, any>
}

interface DecisionResponse {
  number_of_appropriate_graphs: number
  graph_indices: number[]
  graph_configs: GraphConfig[]
}

type ChartType = 'active' | 'stacked' | 'dots' | 'multiple' | 'donut'

const chartTypeMap = {
  0: 'active' as ChartType,
  1: 'stacked' as ChartType,
  2: 'donut' as ChartType,
  3: 'dots' as ChartType,
  4: 'multiple' as ChartType,
}

const getChartTypeInfo = (type: ChartType) => {
  switch (type) {
    case 'active':
      return { label: 'Bar Active', icon: BarChart3 }
    case 'stacked':
      return { label: 'Stacked', icon: BarChart4 }
    case 'dots':
      return { label: 'Line Dots', icon: TrendingUp }
    case 'multiple':
      return { label: 'Multi Line', icon: LineChart }
    case 'donut':
      return { label: 'Donut', icon: PieChart }
  }
}

// Function to call the API endpoint
async function decideGraphFromData(
  csvContent: string,
  userQuery: string = "",
  responseText: string = ""
): Promise<DecisionResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/decide`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        csv_content: csvContent,
        user_query: userQuery,
        response_text: responseText,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Error calling decide API:', error)
    // Fallback response
    return {
      number_of_appropriate_graphs: 0,
      graph_indices: [],
      graph_configs: [],
    }
  }
}

// Helper function to generate colors for chart config
function generateColors(keys: string[]): Record<string, string> {
  const colors = [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
  ]
  
  const colorConfig: Record<string, string> = {}
  keys.forEach((key, index) => {
    colorConfig[key] = colors[index % colors.length]
  })
  
  return colorConfig
}

// Helper function to transform CSV data for charts
function transformDataForChart(
  parsedData: any[],
  config: GraphConfig,
  chartIndex: number
): { data: any[]; chartConfig: ChartConfig } {
  const { primary_key, value_keys } = config.data_keys
  
  // Generate chart config based on the keys
  const allKeys = [primary_key, ...value_keys].filter(Boolean)
  const colors = generateColors(allKeys)
  
  const chartConfig: ChartConfig = {}
  allKeys.forEach(key => {
    chartConfig[key] = {
      label: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      color: colors[key],
    }
  })

  let transformedData: any[] = []

  switch (chartIndex) {
    case 0: // Bar Chart Active
    case 2: // Pie Chart Donut
    case 3: // Line Chart Dots
      // Single value charts
      const valueKey = value_keys[0]
      transformedData = parsedData.map((row, index) => ({
        category: row[primary_key] || `Item ${index + 1}`,
        value: parseFloat(row[valueKey]) || 0,
        fill: colors[primary_key] || colors[valueKey],
      })).filter(item => !isNaN(item.value))
      break

    case 1: // Bar Chart Stacked
    case 4: // Line Chart Multiple
      // Multiple value charts
      transformedData = parsedData.map((row, index) => {
        const item: any = {
          category: row[primary_key] || `Item ${index + 1}`,
        }
        
        value_keys.forEach(key => {
          item[key] = parseFloat(row[key]) || 0
        })
        
        return item
      }).filter(item => 
        value_keys.some(key => !isNaN(item[key]))
      )
      break

    default:
      transformedData = parsedData
  }

  return { data: transformedData.slice(0, 50), chartConfig }
}

const GraphSelector: React.FC<GraphSelectorProps> = React.memo(({
  csvContent,
  userQuery = "",
  responseText = "",
  precomputedGraphDecision,
  onGraphDecisionReady
}) => {
  const [graphDecision, setGraphDecision] = useState<DecisionResponse | null>(null)
  const [parsedData, setParsedData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedChart, setSelectedChart] = useState<ChartType | null>(null)
  const [availableCharts, setAvailableCharts] = useState<ChartType[]>([])

  // Track processed data to prevent unnecessary API calls
  const processedDataRef = useRef<string>('')
  const isProcessingRef = useRef<boolean>(false)
  const callbackRef = useRef(onGraphDecisionReady)

  // Update callback ref when it changes
  useEffect(() => {
    callbackRef.current = onGraphDecisionReady
  }, [onGraphDecisionReady])

  useEffect(() => {
    const processData = async () => {
      // Create a unique key for this data combination
      const dataKey = `${csvContent}-${userQuery}-${responseText}-${!!precomputedGraphDecision}`;

      // Skip if we're already processing or have already processed this exact data
      if (isProcessingRef.current || processedDataRef.current === dataKey) {
        return;
      }

      isProcessingRef.current = true;
      setLoading(true)
      setError(null)

      try {
        // Parse CSV data
        const parseResult = Papa.parse(csvContent, {
          header: true,
          skipEmptyLines: true,
          dynamicTyping: false,
        })

        if (parseResult.errors.length > 0) {
          console.warn('CSV parsing warnings:', parseResult.errors)
        }

        setParsedData(parseResult.data)

        let decision: DecisionResponse;

        // Use precomputed decision if available, otherwise call API
        if (precomputedGraphDecision) {
          console.log('📊 Using precomputed graph decision from localStorage (View Stats)')
          decision = precomputedGraphDecision
        } else {
          console.log('🔄 Fetching fresh graph decision from API (New Response)')
          decision = await decideGraphFromData(csvContent, userQuery, responseText)
          // Call callback with new decision for storage
          if (callbackRef.current && typeof callbackRef.current === 'function') {
            console.log('💾 Storing new graph decision to localStorage')
            callbackRef.current(decision)
          }
        }

        setGraphDecision(decision)

        // Set available charts and select first one
        const charts = decision.graph_indices.map(index => chartTypeMap[index as keyof typeof chartTypeMap]).filter(Boolean)
        setAvailableCharts(charts)
        if (charts.length > 0) {
          setSelectedChart(charts[0])
        }

        // Mark this data as processed
        processedDataRef.current = dataKey;
      } catch (err) {
        console.error('Error processing data:', err)
        setError('Failed to process data for visualization')
      } finally {
        setLoading(false)
        isProcessingRef.current = false;
      }
    }

    if (csvContent) {
      processData()
    }
  }, [csvContent, userQuery, responseText, precomputedGraphDecision])

  const renderChart = () => {
    if (!selectedChart || !graphDecision || !parsedData.length) return null

    const config = graphDecision.graph_configs.find(c => chartTypeMap[c.index as keyof typeof chartTypeMap] === selectedChart)
    if (!config) return null

    const { data, chartConfig } = transformDataForChart(parsedData, config, config.index)
    
    if (!data.length) {
      return (
        <div className="p-8 text-center text-muted-foreground">
          <div className="bg-background rounded-base border-2 border-border p-6">
            No data available for {config.title}
          </div>
        </div>
      )
    }

    const commonProps = {
      data,
      config: chartConfig,
      title: config.title,
      description: config.description,
      className: "w-full p-4",
    }

    switch (selectedChart) {
      case 'active':
        return (
          <BarChartActive
            {...commonProps}
            dataKey="value"
            nameKey="category"
            activeIndex={Math.floor(data.length / 2)}
          />
        )

      case 'stacked':
        return (
          <BarChartStacked
            {...commonProps}
            xAxisKey="category"
            stackedKeys={config.data_keys.value_keys}
          />
        )

      case 'donut':
        return (
          <PieChartDonut
            {...commonProps}
            dataKey="value"
            nameKey="category"
            centerLabel={config.y_axis_label}
          />
        )

      case 'dots':
        return (
          <LineChartDots
            {...commonProps}
            dataKey="value"
            nameKey="category"
          />
        )

      case 'multiple':
        return (
          <LineChartMultiple
            {...commonProps}
            xAxisKey="category"
            lineKeys={config.data_keys.value_keys}
          />
        )

      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground text-sm">Analyzing data and selecting charts...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <Card className="border-destructive/20 bg-destructive/10">
          <CardContent className="p-4">
            <p className="text-destructive font-medium">Error:</p>
            <p className="text-destructive text-sm">{error}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!graphDecision || graphDecision.number_of_appropriate_graphs === 0) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center text-muted-foreground">
          <PieChart className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="font-medium">No suitable visualizations found</p>
          <p className="text-sm mt-2">The data may not be suitable for the available chart types.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Chart Type Selector */}
      <Card className="border-2 border-border shadow-shadow mb-6">
        <CardHeader className="py-3 px-4">
          <CardTitle className="text-sm">
            Recommended Charts ({availableCharts.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="py-2 px-4">
          <div className="flex gap-2 flex-wrap">
            {availableCharts.map((type) => {
              const { label, icon: Icon } = getChartTypeInfo(type)
              return (
                <Button
                  key={type}
                  variant={selectedChart === type ? "default" : "neutral"}
                  size="sm"
                  onClick={() => setSelectedChart(type)}
                  className="flex items-center gap-1 text-xs"
                >
                  <Icon className="w-3 h-3" />
                  {label}
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Chart Display */}
      <div className="flex-1 overflow-auto p-4">
        <div className="h-full">
          {renderChart()}
        </div>
      </div>

      {/* Footer Info */}
      {graphDecision.graph_configs.length > 0 && (
        <div className="text-xs text-muted-foreground text-center py-3 px-4 border-t mt-4">
          Charts automatically selected based on data structure and query analysis
        </div>
      )}
    </div>
  )
})

export default GraphSelector