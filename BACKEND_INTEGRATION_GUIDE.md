# Backend Integration Guide - Groundwater AI Assistant

## Project Overview

This is a React-based groundwater analysis application that integrates with a backend API for intelligent data processing and visualization. The frontend handles session management, data retrieval, local storage persistence, and dynamic chart rendering.

## Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Charts**: Plotly.js, Tremor, Recharts
- **State Management**: React hooks + localStorage
- **HTTP Client**: Native fetch API
- **Routing**: React Router v6

## Backend API Integration

### 1. API Configuration

**File**: `src/lib/api.ts`

```typescript
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";
```

**Environment Variables**:

- `VITE_API_BASE_URL`: Backend server URL (defaults to localhost:3000)

### 2. API Endpoints & Data Flow

#### A. Session Management

**Endpoint**: `POST /chat/new-session`

**Request**:

```typescript
// No body required
{
  method: 'POST',
  headers: { 'Content-Type': 'application/json' }
}
```

**Response**:

```typescript
interface NewSessionResponse {
  session_id: string; // UUID for session tracking
}
```

**Sample Response**:

```json
{
  "session_id": "abc123-def456-ghi789"
}
```

#### B. Chat Message Processing

**Endpoint**: `POST /chat`

**Request**:

```typescript
interface ChatRequest {
  question: string; // User's natural language query
  session_id: string; // Session UUID from new-session
  include_visualization: boolean; // Whether to generate charts
}
```

**Sample Request**:

```json
{
  "question": "What are the groundwater trends in Punjab?",
  "session_id": "abc123-def456-ghi789",
  "include_visualization": true
}
```

**Response**:

```typescript
interface ChatResponse {
  success: boolean;
  session_id: string;
  sql_query: string; // Generated SQL query
  response: string; // AI-generated text response
  explanation: string; // Technical explanation
  data: Array<Record<string, any>>; // Query results
  csv_data: string; // CSV format of data
  error: string; // Error message if any
  visualization: any; // Chart configuration
  metadata: {
    rows_returned: number;
    columns: string[];
    execution_time: string;
    has_visualization: boolean;
    session_id: string;
    context_used: boolean;
    context_queries_count: number;
    mentioned_entities_count: number;
  };
  chat_history: any[];
}
```

**Sample Response**:

```json
{
  "success": true,
  "session_id": "abc123-def456-ghi789",
  "sql_query": "SELECT state, year, avg_water_level FROM groundwater_data WHERE state='Punjab' ORDER BY year",
  "response": "Punjab shows declining groundwater levels over the past 5 years...",
  "explanation": "The query analyzes historical groundwater data for Punjab state...",
  "data": [
    {"state": "Punjab", "year": 2020, "avg_water_level": -15.2},
    {"state": "Punjab", "year": 2021, "avg_water_level": -16.8}
  ],
  "csv_data": "state,year,avg_water_level\nPunjab,2020,-15.2\nPunjab,2021,-16.8",
  "error": "",
  "visualization": {...},
  "metadata": {
    "rows_returned": 2,
    "columns": ["state", "year", "avg_water_level"],
    "execution_time": "0.45s",
    "has_visualization": true,
    "session_id": "abc123-def456-ghi789",
    "context_used": true,
    "context_queries_count": 3,
    "mentioned_entities_count": 1
  },
  "chat_history": [...]
}
```

#### C. Decision Engine API

**Endpoint**: `POST /decide`

**Request**:

```typescript
{
  csv_content: string; // CSV data from chat response
}
```

**Response**:

```typescript
interface DecideResponse {
  srno: number; // Graph type identifier (1,3,4,6,7)
  jsonData: any[]; // Processed data for visualization
}
```

**Sample Response**:

```json
{
  "srno": 1,
  "jsonData": [
    {
      "year": "2020-2021",
      "state": "ODISHA",
      "district": "GAJAPATI",
      "rainfall_mm_total": 1456.78,
      "annual_ground_water_recharge_ham_total": 2345.67
    }
  ]
}
```

## Local Storage Implementation

### 1. Storage Keys & Data Structures

**File**: `src/pages/ChatbotPage.tsx`

```typescript
const SNAP_KEY = "chat_analysis_bundles_v1"; // Analysis data
const MSG_KEY = "chat_messages_v1"; // Chat messages
const ANALYSIS_KEY = "chat_analyses"; // Per-message analysis
```

### 2. Message Storage

```typescript
interface Message {
  id: string; // Unique message ID
  type: "user" | "bot"; // Message type
  content: string; // Message text
  timestamp: Date; // Creation time
  hasChart?: boolean; // Has visualization
  chartData?: any; // Chart configuration
  audioUrl?: string; // Voice message URL
  analysis?: any; // Analysis data
  rightPanelState?: {
    // UI state persistence
    decideData?: { graphNumber: number; data: any[] } | null;
    selectedBundle?: AnalysisBundle | null;
    chartMode?: "trend" | "compare" | "status";
    viewMode?: "chart" | "map";
  };
}
```

### 3. Storage Operations

**Read Messages**:

```typescript
const readMessages = (): Message[] => {
  try {
    const raw = localStorage.getItem(MSG_KEY);
    if (!raw) return [];
    const parsed = JSON.parse(raw) as any[];
    // Revive Date objects
    return parsed.map((m) => ({
      ...m,
      timestamp: m.timestamp ? new Date(m.timestamp) : new Date(),
    }));
  } catch {
    return [];
  }
};
```

**Write Messages**:

```typescript
const writeMessages = (msgs: Message[]) => {
  try {
    localStorage.setItem(
      MSG_KEY,
      JSON.stringify(
        msgs.map((m) => ({
          ...m,
          timestamp:
            m.timestamp instanceof Date
              ? m.timestamp.toISOString()
              : m.timestamp,
        }))
      )
    );
  } catch (e) {
    console.error("Failed to write messages to localStorage:", e);
  }
};
```

## Data Processing Logic

### 1. Message Flow

```
User Input → Backend API → Response Processing → Local Storage → UI Update
```

### 2. Backend Response Processing

**File**: `src/pages/ChatbotPage.tsx` (lines 520-627)

```typescript
const handleSend = async () => {
  // 1. Send to chat API
  const chatResponse: ChatResponse = await sendChatMessage({
    question: currentInput,
    session_id: sessionId,
    include_visualization: true,
  });

  // 2. Process decide API if CSV data available
  let currentDecideData = null;
  if (chatResponse.csv_data) {
    const decideResponse = await sendDecideRequest(chatResponse.csv_data);
    currentDecideData = {
      graphNumber: decideResponse.srno,
      data: decideResponse.jsonData,
    };
    setDecideData(currentDecideData);
  }

  // 3. Create bot response message
  const botResponse: Message = {
    id: `bot_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    type: "bot",
    content: chatResponse.response,
    timestamp: new Date(),
    hasChart: !!chatResponse.visualization,
    chartData: chatResponse.visualization,
    analysis: chatResponse.data,
    rightPanelState: {
      decideData: currentDecideData,
      selectedBundle: null,
      chartMode: "trend",
      viewMode: "chart",
    },
  };

  // 4. Update state and persist
  setMessages((prev) => [...prev, botResponse]);
};
```

### 3. Fallback Mock Data

When backend is unavailable, the system generates mock responses:

```typescript
const generateMockResponse = (query: string): Message => {
  const lowerQuery = query.toLowerCase();

  // Generate different responses based on query content
  if (lowerQuery.includes("punjab") || lowerQuery.includes("rajasthan")) {
    return {
      content:
        "Based on the latest data, Punjab shows critical groundwater depletion...",
      analysis: {
        trends: {
          type: "trend",
          series: [
            {
              label: "Punjab",
              data: [-0.9, -1.1, -1.3, -1.5, -1.7],
              years: [2020, 2021, 2022, 2023, 2024],
            },
          ],
        },
        rawData: [{ state: "Punjab", level: -15.2, trend: -2.3 }],
      },
    };
  }
  // ... more mock patterns
};
```

## Visualization System

### 1. Dynamic Graph Rendering

**File**: `src/components/DynamicGraph.tsx`

The system supports multiple graph types based on the `srno` from decide API:

- **Graph 1**: Time series line charts (Tremor LineChart)
- **Graph 3**: Area charts
- **Graph 4**: Scatter plots
- **Graph 6**: Bar charts
- **Graph 7**: Tabbed analytics dashboard

### 2. Graph Data Processing

**Example - Graph 1** (`src/components/graph/Graph1.jsx`):

```javascript
// Input: Backend data with mixed field types
const data = [
  {
    year: "2020-2021",
    state: "ODISHA",
    district: "GAJAPATI",
    rainfall_mm_total: "1456.78",
    annual_ground_water_recharge_ham_total: "2345.67",
  },
];

// Processing: Auto-detect numeric fields and format
const numericFields = Object.keys(data[0]).filter((key) => {
  return (
    key !== "year" &&
    key !== "state" &&
    key !== "district" &&
    !isNaN(parseFloat(data[0][key]))
  );
});

// Output: Tremor-compatible format
const transformedData = data.map((item) => ({
  date: item.year,
  "Rainfall Mm Total": parseFloat(item.rainfall_mm_total),
  "Annual Ground Water Recharge Ham Total": parseFloat(
    item.annual_ground_water_recharge_ham_total
  ),
}));
```

### 3. Chart Configuration

Charts use consistent styling and responsive design:

```javascript
<LineChart
  data={transformedData}
  index="date"
  categories={cleanFieldNames}
  colors={["blue", "red", "green", "purple", "orange"]}
  valueFormatter={valueFormatter} // Formats large numbers (K/M/B)
  showLegend={true}
  className="mt-6 h-48"
  connectNulls={true}
/>
```

## Error Handling & Resilience

### 1. API Error Handling

```typescript
try {
  const response = await sendChatMessage(request);
  // Process successful response
} catch (error) {
  console.error("Backend error:", error);
  // Fallback to mock response
  const botResponse = generateMockResponse(currentInput);
  setMessages((prev) => [...prev, botResponse]);
}
```

### 2. Session Recovery

```typescript
const initializeSession = useCallback(async () => {
  if (sessionId || isInitializingSession) return;

  try {
    const response = await createNewSession();
    setSessionId(response.session_id);
  } catch (error) {
    console.error("Failed to create session:", error);
    // Continue with mock data if backend unavailable
  }
}, [sessionId, isInitializingSession]);
```

### 3. Data Persistence Recovery

```typescript
// Initialize messages from localStorage or with welcome message
useEffect(() => {
  const loaded = readMessages();
  if (loaded.length > 0) {
    setMessages(loaded);
  } else {
    const welcome = {
      id: "welcome",
      type: "bot" as const,
      content: "Hello! I'm your AI assistant for groundwater analysis...",
      timestamp: new Date(),
    };
    setMessages([welcome]);
    writeMessages([welcome]);
  }
}, []);
```

## Sample Input/Output Examples

### Example 1: Trend Analysis Query

**Input**: "Show me groundwater trends in Punjab over the last 5 years"

**Backend Response**:

```json
{
  "success": true,
  "sql_query": "SELECT year, avg_water_level, trend_percentage FROM groundwater_trends WHERE state='Punjab' AND year >= 2019 ORDER BY year",
  "response": "Punjab shows a concerning declining trend in groundwater levels. The average water level has dropped from -12.5m in 2019 to -18.7m in 2024, representing a 33% decline over 5 years.",
  "data": [
    { "year": 2019, "avg_water_level": -12.5, "trend_percentage": -2.1 },
    { "year": 2020, "avg_water_level": -13.8, "trend_percentage": -2.8 },
    { "year": 2021, "avg_water_level": -15.2, "trend_percentage": -3.1 },
    { "year": 2022, "avg_water_level": -16.9, "trend_percentage": -2.9 },
    { "year": 2023, "avg_water_level": -17.8, "trend_percentage": -3.2 },
    { "year": 2024, "avg_water_level": -18.7, "trend_percentage": -3.5 }
  ],
  "csv_data": "year,avg_water_level,trend_percentage\n2019,-12.5,-2.1\n2020,-13.8,-2.8...",
  "metadata": {
    "rows_returned": 6,
    "columns": ["year", "avg_water_level", "trend_percentage"],
    "execution_time": "0.23s"
  }
}
```

**Decide API Response**:

```json
{
  "srno": 1,
  "jsonData": [
    {
      "year": "2019-2020",
      "state": "PUNJAB",
      "district": "LUDHIANA",
      "water_level_m": "-12.5",
      "trend_percent": "-2.1"
    }
  ]
}
```

**Frontend Processing**:

1. Store response in localStorage
2. Render Graph1 with time series data
3. Display AI response text
4. Update right panel with chart controls

### Example 2: Comparison Query

**Input**: "Compare groundwater status between Punjab and Kerala"

**Backend Response**:

```json
{
  "success": true,
  "sql_query": "SELECT state, category, COUNT(*) as district_count FROM district_status WHERE state IN ('Punjab', 'Kerala') GROUP BY state, category",
  "response": "Punjab has 75% over-exploited districts while Kerala has only 15% over-exploited districts. Kerala shows better groundwater management with 60% districts in safe category.",
  "data": [
    { "state": "Punjab", "category": "Over-exploited", "district_count": 18 },
    { "state": "Punjab", "category": "Critical", "district_count": 4 },
    { "state": "Punjab", "category": "Safe", "district_count": 2 },
    { "state": "Kerala", "category": "Over-exploited", "district_count": 2 },
    { "state": "Kerala", "category": "Safe", "district_count": 8 }
  ]
}
```

## Advanced Features

### 1. Voice Input Integration

The application supports voice input with visual feedback:

```typescript
// Voice recording state
const [audioPreviewUrl, setAudioPreviewUrl] = useState<string | null>(null);

// When voice input is detected
if (audioPreviewUrl) {
  playAvatarAudio("/Recording.wav");
  setIsResponding(true);
  const responseDuration = 2000 + Math.random() * 2000; // 2-4 seconds
  setTimeout(() => setIsResponding(false), responseDuration);
}
```

### 2. 3D Avatar Integration

**File**: `src/components/TalkingHeadAssistant.tsx`

The app includes a 3D talking avatar using Three.js:

- GLB model loading: `68d183bcfdc93371e7811524.glb`
- Lip-sync animation during responses
- Fallback to animated GIF when 3D fails

### 3. Interactive Map Component

**File**: `src/components/InteractiveMap.tsx`

Displays groundwater data on India map:

- **Data Sources**:
  - `/india_states.json` - State boundaries
  - `/india_districts.json` - District boundaries
  - `/CentralReport_with_category.csv` - Groundwater data
- **Color Coding**:
  - Safe: Green (#22c55e)
  - Semi-Critical: Yellow (#fbbf24)
  - Critical: Orange (#f97316)
  - Over-exploited: Red (#dc2626)

### 4. Analysis Bundle System

Complex data structures for persistent analysis:

```typescript
interface AnalysisBundle {
  messageId: string;
  createdAt: string;
  queryText: string;
  sections: {
    trend: AnalysisSection;
    comparison: AnalysisSection;
    status: AnalysisSection;
  };
}

interface AnalysisSection {
  chart: {
    type: "trend" | "comparison" | "district" | "status";
    region?: string;
  };
  rawData: Array<Record<string, string | number>>;
  sql: string;
  explain: string;
}
```

## UI State Management

### 1. Right Panel State Persistence

Each message stores its UI state for consistent experience:

```typescript
rightPanelState?: {
  decideData?: {graphNumber: number, data: any[]} | null;
  selectedBundle?: AnalysisBundle | null;
  chartMode?: "trend" | "compare" | "status";
  viewMode?: "chart" | "map";
};
```

### 2. Chart Mode Switching

Users can switch between different chart views:

- **Trend**: Time series analysis
- **Compare**: Regional comparisons
- **Status**: Category distributions
- **Map**: Geographic visualization

### 3. Responsive Design

The interface adapts to different screen sizes:

- Resizable panels using `react-resizable-panels`
- Mobile-optimized chart rendering
- Collapsible sidebar for small screens

## Performance Optimizations

### 1. Lazy Loading

Charts are loaded on-demand:

```typescript
const Graph1 = lazy(() => import("./graph/Graph1.jsx"));
const Graph3 = lazy(() => import("./graph/Graph3.jsx"));
// ... other graphs

<Suspense fallback={<LoadingSpinner />}>
  <GraphComponent data={data} />
</Suspense>;
```

### 2. Memoization

Expensive operations are memoized:

```typescript
const chartConfig = useMemo(() => generateChartData(), [data, mode]);
```

### 3. Efficient Re-renders

State updates are batched and optimized:

```typescript
// Batch multiple state updates
setMessages((prev) => {
  const newMessages = [...prev, userMessage, botResponse];
  return newMessages;
});
```

## Testing & Development

### 1. Test API Button

Built-in API testing functionality:

```typescript
<Button
  onClick={async () => {
    const testResponse = await sendChatMessage({
      question: "What are the top 3 states with highest groundwater recharge?",
      session_id: sessionId,
      include_visualization: false,
    });
    // Process test response...
  }}
>
  Test API
</Button>
```

### 2. Development Mode Features

- Console logging for debugging
- Mock data fallbacks
- Error boundary handling
- Hot reload support

### 3. Graph Testing Page

Dedicated test page at `/test-graph1` for validating chart rendering with real data formats.

## Deployment Configuration

### 1. Environment Setup

```bash
# Development
npm run dev

# Production build
npm run build

# Preview build
npm run preview
```

### 2. Environment Variables

- `VITE_API_BASE_URL`: Backend API endpoint
- Development defaults to `http://localhost:3000`
- Production should point to deployed backend

### 3. Static Assets

Required public files:

- `/india_states.json` - Geographic data
- `/india_districts.json` - District boundaries
- `/CentralReport_with_category.csv` - Groundwater dataset
- `/Recording.wav` - Audio feedback
- `/68d183bcfdc93371e7811524.glb` - 3D avatar model

## Integration Checklist

When implementing this system in a new project:

✅ **Backend API Requirements**:

- POST `/chat/new-session` - Session creation
- POST `/chat` - Message processing
- POST `/decide` - Graph type selection

✅ **Frontend Dependencies**:

- React 18+ with TypeScript
- Tremor for charts
- Plotly.js for advanced visualizations
- Three.js for 3D avatar
- Leaflet for maps

✅ **Data Format Compatibility**:

- CSV responses from backend
- Numeric fields as strings or numbers
- Year field for time series
- State/district for geographic context

✅ **Storage Requirements**:

- localStorage for persistence
- Session management
- Message history
- Analysis bundles

This comprehensive guide covers all aspects of the backend integration, data flow, storage mechanisms, and visualization logic in your groundwater analysis application.
