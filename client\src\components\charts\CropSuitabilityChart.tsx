import React from 'react';

interface CropSuitabilityChartProps {
  data?: any[];
  className?: string;
}

const CropSuitabilityChart: React.FC<CropSuitabilityChartProps> = ({ data, className }) => {
  return (
    <div className={`p-4 border rounded-lg bg-white ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Crop Suitability Analysis</h3>
      <div className="h-64 flex items-center justify-center text-gray-500">
        Crop Suitability Chart Placeholder
      </div>
    </div>
  );
};

export default CropSuitabilityChart;

