import React from 'react';
import { Check, X } from 'lucide-react';
import FloatingBackground from './FloatingBackground';

interface ComparisonItem {
  feature: string;
  jalDrishti: boolean;
  traditional: boolean;
  highlight?: boolean;
}

const ComparisonSection: React.FC = () => {
  const comparisonItems: ComparisonItem[] = [
    { 
      feature: "Multilingual Natural Language Queries", 
      jalDrishti: true, 
      traditional: false,
      highlight: true
    },
    { 
      feature: "Groundwater Data Analysis", 
      jalDrishti: true, 
      traditional: false 
    },
    { 
      feature: "AI-Powered Insights & Recommendations", 
      jalDrishti: true, 
      traditional: false,
      highlight: true
    },
    { 
      feature: "Real-time Water Data Access", 
      jalDrishti: true, 
      traditional: true,
      highlight: false
    },
    { 
      feature: "Voice Input/Output Support", 
      jalDrishti: true, 
      traditional: false 
    },
    { 
      feature: "Interactive Water Data Visualization", 
      jalDrishti: true, 
      traditional: true,
      highlight: true
    },
    { 
      feature: "Knowledge Graph Intelligence", 
      jalDrishti: true, 
      traditional: false 
    },
  ];

  return (
    <section id="comparison" className="py-14 sm:py-16 md:py-20 lg:py-24 bg-[#dcebfe] relative overflow-hidden">
      <FloatingBackground count={15} opacity={0.05} />
      
      <div className="container mx-auto px-4 sm:px-6 md:px-8 max-w-6xl">
        <div className="text-center mb-10 sm:mb-12 md:mb-16">
          <h2 className="text-3xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4">
            Why Choose <span className="text-[#5294ff]">Jal Drishti</span>
          </h2>
          <p className="text-lg sm:text-lg text-gray-700 max-w-3xl mx-auto px-2">
            See how Jal Drishti's INGRES AI transforms groundwater data analysis compared to traditional methods
          </p>
        </div>
        
        <div className="neo-border bg-white overflow-hidden">
          {/* Header */}
          <div className="grid grid-cols-3 text-center font-bold border-b border-black">
            <div className="p-4 sm:p-5 border-r border-black">Features</div>
            <div className="p-4 sm:p-5 bg-[#5294ff]/20 border-r border-black">Jal Drishti - INGRES AI Chatbot</div>
            <div className="p-4 sm:p-5">Traditional Water Data Systems</div>
          </div>

          {/* Comparison Items */}
          {comparisonItems.map((item, index) => (
            <div 
              key={index} 
              className={`grid grid-cols-3 text-center border-b border-black last:border-b-0 ${item.highlight ? 'bg-[#5294ff]/5' : ''}`}
            >
              <div className="p-4 sm:p-5 border-r border-black text-left font-medium">
                {item.feature}
              </div>
              <div className="p-4 sm:p-5 border-r border-black flex justify-center items-center">
                {item.jalDrishti ? (
                  <Check size={24} className="text-green-500" />
                ) : (
                  <X size={24} className="text-red-500" />
                )}
              </div>
              <div className="p-4 sm:p-5 flex justify-center items-center">
                {item.traditional ? (
                  <Check size={24} className="text-green-500" />
                ) : (
                  <X size={24} className="text-red-500" />
                )}
              </div>
            </div>
          ))}
        </div>

      </div>
    </section>
  );
};

export default ComparisonSection; 