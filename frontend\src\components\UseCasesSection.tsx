import React, { useState } from 'react';
import { Code, PieChart, Users, Database, Briefcase, LayoutDashboard, LineChart } from 'lucide-react';
import FloatingBackground from './FloatingBackground';

interface UseCase {
  title: string;
  icon: React.ReactNode;
  description: string;
  examples: string[];
  benefits: string[];
  result: string;
  query: string;
}

const UseCasesSection: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  const useCases: UseCase[] = [
    {
        title: "Water Resource Manager",
        icon: <Code className="w-6 h-6 text-green-600" />,
        description: "Water resource managers can analyze groundwater data and generate reports without complex database queries.",
        examples: [
          "Monitor groundwater levels across different districts",
          "Analyze extraction rates and recharge patterns",
          "Generate compliance reports for regulatory authorities"
        ],
        benefits: [
          "Save time on complex data analysis",
          "Access real-time groundwater insights",
          "Faster decision-making for water allocation"
        ],
        query: "Show me groundwater extraction rates in Gujarat for the last 5 years and identify critical zones",
        result: "A comprehensive analysis of groundwater extraction trends in Gujarat with critical zone identification"
      },
    {
      title: "Policy Maker",
      icon: <PieChart className="w-6 h-6 text-blue-600" />,
      description: "Policy makers can access groundwater insights and trends to inform water resource policies.",
      examples: [
        "Analyze groundwater availability across states",
        "Compare extraction vs recharge rates by region",
        "Identify areas requiring immediate intervention"
      ],
      benefits: [
        "Data-driven policy formulation",
        "Real-time monitoring of water resources",
        "Evidence-based decision making"
      ],
      query: "What is the groundwater availability status across all Indian states for 2024?",
      result: "A comprehensive state-wise groundwater availability report with criticality levels and recommendations"
    },
    {
        title: "Research Scientist",
        icon: <LineChart className="w-6 h-6 text-red-600" />,
        description: "Researchers can access comprehensive groundwater datasets for academic studies and research projects.",
        examples: [
          "Study long-term groundwater trends and patterns",
          "Analyze correlation between rainfall and recharge",
          "Investigate water quality parameters across regions"
        ],
        benefits: [
          "Access to 155+ data columns from 2012-2025",
          "Multilingual query support for diverse research teams",
          "Export capabilities for research publications"
        ],
        query: "Analyze the correlation between monsoon rainfall and groundwater recharge in Tamil Nadu over the last decade",
        result: "Statistical analysis showing correlation between rainfall patterns and groundwater recharge with visualizations"
      },
    {
      title: "Agricultural Farmer",
      icon: <Users className="w-6 h-6 text-purple-600" />,
      description: "Farmers can access groundwater information in their local language to plan irrigation and crop selection.",
      examples: [
        "Check groundwater availability in their district",
        "Get crop suitability recommendations based on water availability",
        "Plan irrigation schedules based on water levels"
      ],
      benefits: [
        "Voice input in regional languages",
        "Simple, non-technical interface",
        "Actionable insights for farming decisions"
      ],
      query: "मेरे जिले में भूजल स्तर कैसा है और कौन सी फसलें उगाना बेहतर होगा?",
      result: "आपके जिले का भूजल स्तर और फसल सुझाव (Groundwater level in your district and crop recommendations)"
    },
    {
      title: "Environmental Consultant",
      icon: <Briefcase className="w-6 h-6 text-yellow-600" />,
      description: "Environmental consultants can analyze groundwater data for sustainability assessments and environmental impact studies.",
      examples: [
        "Assess groundwater sustainability in project areas",
        "Monitor water quality parameters and contamination",
        "Generate environmental impact reports"
      ],
      benefits: [
        "Comprehensive groundwater data access",
        "Multi-year trend analysis capabilities",
        "Export data for client reports"
      ],
      query: "Show me groundwater quality parameters and contamination levels in industrial areas of Maharashtra",
      result: "Detailed groundwater quality analysis with contamination mapping and risk assessment for industrial areas"
    },
  ];

  return (
    <section id="use-cases" className="py-14 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
      <FloatingBackground count={10} opacity={0.02} />
      
      <div className="container mx-auto px-4 sm:px-6 md:px-8 max-w-6xl">
        <div className="text-center mb-10 sm:mb-12 md:mb-16">
          <h2 className="text-3xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4">
            Jal Drishti <span className="text-[#5294ff]">Use Cases</span>
          </h2>
          <p className="text-lg sm:text-lg text-gray-700 max-w-3xl mx-auto px-2">
            Discover how Jal Drishti's INGRES AI can transform groundwater data analysis for different stakeholders
          </p>
        </div>
        
        {/* Tabs */}
        <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 sm:mb-10">
          {useCases.map((useCase, index) => (
            <button
              key={index}
              onClick={() => setActiveTab(index)}
              className={`neo-border px-4 py-2 sm:px-5 sm:py-3 flex items-center gap-2 transition-all ${
                activeTab === index 
                  ? 'bg-[#5294ff] font-bold' 
                  : 'bg-white hover:bg-gray-50'
              }`}
              style={{ transform: `rotate(${Math.random() * 0.6 - 0.3}deg)` }}
            >
              {useCase.icon}
              <span className="text-base sm:text-base">{useCase.title}</span>
            </button>
          ))}
        </div>
        
        {/* Active Tab Content */}
        <div 
          className="neo-border bg-white p-5 sm:p-6 md:p-8"
          style={{ transform: 'rotate(-0.2deg)' }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
            <div>
              <h3 className="text-xl sm:text-2xl font-bold mb-4 flex items-center gap-2">
                {useCases[activeTab].icon}
                {useCases[activeTab].title}
              </h3>
              
              <p className="text-base sm:text-lg text-gray-700 mb-6">
                {useCases[activeTab].description}
              </p>
              
              <div className="mb-6">
                <h4 className="font-bold text-lg mb-2">Example Use Cases:</h4>
                <ul className="space-y-2">
                  {useCases[activeTab].examples.map((example, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <span className="bg-[#FFDB58] rounded-full w-5 h-5 flex-shrink-0 flex items-center justify-center text-sm font-bold mt-0.5">
                        {i + 1}
                      </span>
                      <span className="text-base text-gray-700">{example}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-bold text-lg mb-2">Key Benefits:</h4>
                <ul className="space-y-2">
                  {useCases[activeTab].benefits.map((benefit, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <span className="text-green-500 flex-shrink-0 mt-0.5">✓</span>
                      <span className="text-base text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="neo-border bg-[#dcebfe] p-4 sm:p-5 md:p-6 flex flex-col">
              <h4 className="font-bold text-lg mb-3">Example Query:</h4>
              
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                  <Users className="w-4 h-4 text-gray-600" />
                </div>
                <div className="neo-border bg-white px-4 py-3 flex-1 text-base italic">
                  "{useCases[activeTab].query}"
                </div>
              </div>
              
              <div className="flex-grow flex flex-col items-center justify-center p-4">
                <LayoutDashboard className="w-12 h-12 text-gray-300 mb-4" />
                <p className="text-center text-gray-500 italic">
                  Result: {useCases[activeTab].result}
                </p>
              </div>
              
              <div className="mt-4 text-center">
                <a 
                  href={import.meta.env.VITE_NEOBASE_APP_URL}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="neo-button inline-flex items-center justify-center py-2 px-4 sm:py-3 sm:px-6 font-bold text-base"
                >
                  Try INGRES AI For Your Use Case
                </a>
              </div>
            </div>
          </div>
        </div>
        <p className='text-center text-gray-500 mt-8 text-lg'>Jal Drishti serves water stakeholders everywhere - <span className='text-black font-medium'>Government Agencies, Research Institutions, Agricultural Communities, Environmental Organizations, and more.</span></p>
      </div>
    </section>
  );
};

export default UseCasesSection; 