@import url('https://fonts.googleapis.com/css2?family=Archivo:wght@400;500;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --border-radius: 5px;
  --box-shadow-x: 4px;
  --box-shadow-y: 4px;
  --reverse-box-shadow-x: -4px;
  --reverse-box-shadow-y: -4px;

  --heading-font-weight: 700;
  --base-font-weight: 500;

  --background: oklch(93.46% 0.0304 254.32);
  --secondary-background: oklch(100% 0 0);
  --foreground: oklch(0% 0 0);
  --main-foreground: oklch(0% 0 0);

  --main: oklch(67.47% 0.1725 259.61);
  --border: oklch(0% 0 0);
  --ring: oklch(0% 0 0);
  --overlay: oklch(0% 0 0 / 0.8);

  --shadow: var(--box-shadow-x) var(--box-shadow-y) 0px 0px var(--border);

  --chart-1: oklch(67.47% 0.1726 259.49);
  --chart-2: oklch(67.28% 0.2147 24.22);
  --chart-3: oklch(86.03% 0.176 92.36);
  --chart-4: oklch(79.76% 0.2044 153.08);
  --chart-5: oklch(66.34% 0.1806 277.2);
  --chart-active-dot: #000;
}

.dark {
  --background: oklch(29.23% 0.0626 270.49);
  --secondary-background: oklch(100% 0 0);
  --main: oklch(67.47% 0.1726 259.49);
  --ring: oklch(100% 0 0);
  --foreground: oklch(92.49% 0 0);
  --main-foreground: oklch(0% 0 0);
  --border: oklch(0% 0 0);
  --overlay: rgba(0, 0, 0, 0.8);
  --shadow: 4px 4px 0px 0px var(--border);
}

/* Additional color variables for Tailwind compatibility */
:root {
  --color-main: var(--main);
  --color-background: var(--background);
  --color-secondary-background: var(--secondary-background);
  --color-foreground: var(--foreground);
  --color-main-foreground: var(--main-foreground);
  --color-border: var(--border);
  --color-overlay: var(--overlay);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --spacing-boxShadowX: var(--box-shadow-x);
  --spacing-boxShadowY: var(--box-shadow-y);
  --spacing-reverseBoxShadowX: var(--reverse-box-shadow-x);
  --spacing-reverseBoxShadowY: var(--reverse-box-shadow-y);

  --radius-base: var(--border-radius);

  --font-weight-base: var(--base-font-weight);
  --font-weight-heading: var(--heading-font-weight);

  --shadow-shadow: var(--shadow);
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Archivo', sans-serif;
    /**Can use Archivo**/
    letter-spacing: 0.01em;
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components {
  .neo-border {
    border: 4px solid var(--border);
    border-radius: var(--radius-base);
    box-shadow: var(--shadow);
  }

  .neo-button {
    @apply neo-border px-4 py-2 font-bold text-base transition-all;
    background-color: var(--main);
    color: var(--main-foreground);

    &:hover {
      transform: translateY(-2px);
      box-shadow: calc(var(--spacing-boxShadowX) + 2px) calc(var(--spacing-boxShadowY) + 2px) 0px 0px var(--border);
    }

    &:active {
      transform: translateY(0px);
      box-shadow: var(--shadow);
    }
  }

  .neo-button:disabled {
    background-color: var(--secondary-background);
    color: var(--foreground);
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(0);
    box-shadow: var(--shadow);
  }

  .neo-button-secondary {
    @apply neo-border px-4 py-2 font-bold text-base transition-all;
    background-color: var(--secondary-background);
    color: var(--foreground);

    &:hover {
      transform: translateY(-2px);
      box-shadow: calc(var(--spacing-boxShadowX) + 2px) calc(var(--spacing-boxShadowY) + 2px) 0px 0px var(--border);
    }

    &:active {
      transform: translateY(0px);
      box-shadow: var(--shadow);
    }
  }

  .neo-input:disabled {
    background-color: var(--secondary-background);
    opacity: 0.5;
    cursor: not-allowed;
  }

  .neo-input {
    @apply neo-border px-4 py-2 text-base focus:outline-none;
    background-color: var(--secondary-background);
    color: var(--foreground);

    &:focus {
      ring: 2px solid var(--ring);
      ring-offset: 2px;
    }
  }

  /* Custom select styling */
  select.neo-input {
    @apply cursor-pointer bg-none;
  }

  select.neo-input option {
    @apply py-2 px-4 min-h-[40px] flex items-center;
  }

  /* Remove default select arrow in modern browsers */
  select.neo-input::-ms-expand {
    display: none;
  }

  @supports (-webkit-appearance: none) or (-moz-appearance: none) {
    select.neo-input {
      -webkit-appearance: none;
      -moz-appearance: none;
    }
  }

  .message-bubble {
    @apply neo-border p-4 mb-4 break-words;
  }

  .message-bubble-user {
    @apply message-bubble ml-auto w-fit max-w-[95%] md:max-w-[70%];
    background-color: var(--secondary-background);
    color: var(--foreground);
  }

  .message-bubble-ai {
    @apply message-bubble w-fit max-w-[95%] md:max-w-[75%];
    background-color: var(--main);
    color: var(--main-foreground);
    opacity: 0.9;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-30px) translateX(30px) rotate(10deg);
  }
  50% {
    transform: translateY(0px) translateX(60px) rotate(0deg);
  }
  75% {
    transform: translateY(30px) translateX(30px) rotate(-10deg);
  }
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
}

.animate-float {
  animation: float infinite ease-in-out;
}