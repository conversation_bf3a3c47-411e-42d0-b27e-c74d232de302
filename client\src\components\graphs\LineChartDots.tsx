"use client"

import { TrendingUp } from "lucide-react"
import { CartesianGrid, Dot, Line, LineChart } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface LineChartDotsProps {
  data: Array<{
    [key: string]: any;
    fill?: string;
  }>;
  config: ChartConfig;
  title?: string;
  description?: string;
  dataKey: string;
  nameKey: string;
  className?: string;
}

export default function LineChartDots({
  data,
  config,
  title = "Line Chart - Dots Colors",
  description = "Data visualization",
  dataKey,
  nameKey,
  className = ""
}: LineChartDotsProps) {
  return (
    <Card className={`bg-secondary-background text-foreground ${className}`}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          className="[&.recharts-layer_path]:stroke-black [&.recharts-layer_path]:dark:stroke-white"
          config={config}
        >
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              top: 24,
              left: 24,
              right: 24,
            }}
          >
            <CartesianGrid vertical={false} />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="line"
                  nameKey={dataKey}
                  hideLabel
                />
              }
            />
            <Line
              dataKey={dataKey}
              type="natural"
              stroke={`var(--color-${dataKey})`}
              strokeWidth={2}
              dot={({ payload, ...props }) => {
                return (
                  <Dot
                    key={payload[nameKey]}
                    r={5}
                    cx={props.cx}
                    cy={props.cy}
                    fill={payload.fill || `var(--color-${payload[nameKey]})`}
                    stroke={payload.fill || `var(--color-${payload[nameKey]})`}
                  />
                )
              }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing data visualization results
        </div>
      </CardFooter>
    </Card>
  )
}
