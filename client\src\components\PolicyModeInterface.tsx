import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Loader2, CheckCircle, Clock, AlertCircle, Download, Eye, Code, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';

interface Task {
  id: string;
  title: string;
  description: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  result?: any;
  code?: string;
  output?: any;
  execution_time?: string;
}

interface PolicyModeInterfaceProps {
  query: string;
  onComplete: (report: any) => void;
}

const PolicyModeInterface: React.FC<PolicyModeInterfaceProps> = ({ query, onComplete }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1);
  const [planId, setPlanId] = useState<string>('');
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [executionResults, setExecutionResults] = useState<any[]>([]);
  const [finalReport, setFinalReport] = useState<any>(null);

  // Step 1: Generate task plan
  const generateTaskPlan = async () => {
    setIsGeneratingPlan(true);
    try {
      const response = await fetch('http://localhost:8000/orchestrate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
      });

      if (!response.ok) throw new Error('Failed to generate plan');
      
      const data = await response.json();
      setPlanId(data.plan_id);
      
      const formattedTasks = data.tasks.map((task: any, index: number) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        type: task.type,
        status: 'pending' as const,
        progress: 0
      }));
      
      setTasks(formattedTasks);
      toast.success('Task plan generated successfully!');
    } catch (error) {
      console.error('Error generating plan:', error);
      toast.error('Failed to generate task plan');
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Step 2: Execute tasks one by one
  const executeTasks = async () => {
    if (tasks.length === 0) return;
    
    setIsExecuting(true);
    setCurrentTaskIndex(0);
    
    try {
      const response = await fetch('http://localhost:8000/execute-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ plan_id: planId })
      });

      if (!response.ok) throw new Error('Failed to start execution');
      
      // Poll for execution status
      pollExecutionStatus();
    } catch (error) {
      console.error('Error executing tasks:', error);
      toast.error('Failed to execute tasks');
      setIsExecuting(false);
    }
  };

  // Poll execution status
  const pollExecutionStatus = async () => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`http://localhost:8000/execution-status/${planId}`);
        if (!response.ok) throw new Error('Failed to get status');
        
        const data = await response.json();
        
        // Update tasks with current status
        setTasks(prevTasks => 
          prevTasks.map(task => {
            const taskResult = data.task_results.find((r: any) => r.task_id === task.id);
            if (taskResult) {
              return {
                ...task,
                status: taskResult.status,
                progress: taskResult.status === 'completed' ? 100 : 
                         taskResult.status === 'running' ? 50 : 0,
                result: taskResult.result,
                code: taskResult.code_generated,
                output: taskResult.execution_result,
                execution_time: taskResult.execution_time
              };
            }
            return task;
          })
        );

        // Update current task index
        const runningTaskIndex = data.task_results.findIndex((r: any) => r.status === 'running');
        if (runningTaskIndex !== -1) {
          setCurrentTaskIndex(runningTaskIndex);
        }

        // Check if all tasks are completed
        if (data.status === 'completed') {
          clearInterval(pollInterval);
          setIsExecuting(false);
          setExecutionResults(data.task_results);
          toast.success('All tasks completed successfully!');
          
          // Auto-generate report
          generateReport(data.task_results);
        } else if (data.status === 'failed') {
          clearInterval(pollInterval);
          setIsExecuting(false);
          toast.error('Task execution failed');
        }
      } catch (error) {
        console.error('Error polling status:', error);
      }
    }, 2000);
  };

  // Step 3: Generate final report
  const generateReport = async (taskResults: any[]) => {
    setIsGeneratingReport(true);
    try {
      const response = await fetch('http://localhost:8000/generate-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          plan_id: planId,
          original_query: query,
          task_results: taskResults,
          report_type: 'comprehensive',
          format: 'html'
        })
      });

      if (!response.ok) throw new Error('Failed to generate report');
      
      const reportData = await response.json();
      setFinalReport(reportData);
      onComplete(reportData);
      toast.success('Report generated successfully!');
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report');
    } finally {
      setIsGeneratingReport(false);
    }
  };

  // Auto-start plan generation when component mounts
  useEffect(() => {
    if (query) {
      generateTaskPlan();
    }
  }, [query]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'running':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-main" />
            Policy Analysis Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="w-4 h-4" />
            <AlertTitle>Complex Query Detected</AlertTitle>
            <AlertDescription>
              Your query requires comprehensive analysis. The system will break it down into tasks, execute them sequentially, and generate a detailed policy report.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Task Generation Status */}
      {isGeneratingPlan && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Loader2 className="w-5 h-5 animate-spin text-main" />
              <span className="font-medium">Generating task plan...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Task List */}
      {tasks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Task Execution Plan</span>
              {!isExecuting && tasks.length > 0 && (
                <Button onClick={executeTasks} className="ml-4">
                  Start Execution
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {tasks.map((task, index) => (
              <div
                key={task.id}
                className={`p-4 border-2 rounded-lg transition-all ${
                  index === currentTaskIndex ? 'border-main bg-main/5' : 'border-border'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {getStatusIcon(task.status)}
                      <h4 className="font-medium">{task.title}</h4>
                      <Badge className={getStatusColor(task.status)}>
                        {task.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{task.description}</p>
                    {task.status === 'running' && (
                      <Progress value={task.progress} className="mb-2" />
                    )}
                  </div>
                </div>
                
                {/* Task Results */}
                {task.status === 'completed' && task.result && (
                  <div className="mt-3 space-y-2">
                    {task.code && (
                      <details className="bg-gray-50 rounded p-3">
                        <summary className="cursor-pointer font-medium flex items-center gap-2">
                          <Code className="w-4 h-4" />
                          View Generated Code
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                          <code>{task.code}</code>
                        </pre>
                      </details>
                    )}
                    
                    {task.output && (
                      <details className="bg-gray-50 rounded p-3">
                        <summary className="cursor-pointer font-medium flex items-center gap-2">
                          <Eye className="w-4 h-4" />
                          View Output
                        </summary>
                        <div className="mt-2 text-xs">
                          <pre className="bg-gray-100 p-2 rounded overflow-x-auto">
                            {JSON.stringify(task.output, null, 2)}
                          </pre>
                        </div>
                      </details>
                    )}
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Report Generation Status */}
      {isGeneratingReport && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Loader2 className="w-5 h-5 animate-spin text-main" />
              <span className="font-medium">Generating comprehensive policy report...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Final Report */}
      {finalReport && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Policy Analysis Complete</span>
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                Download Report
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <CheckCircle className="w-4 h-4" />
              <AlertTitle>Analysis Complete</AlertTitle>
              <AlertDescription>
                Your comprehensive policy analysis has been completed. The report includes detailed findings, recommendations, and implementation roadmap.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PolicyModeInterface;
