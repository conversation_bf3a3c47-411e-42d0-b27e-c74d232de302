@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --border-radius: 5px;
  --box-shadow-x: 4px;
  --box-shadow-y: 4px;
  --reverse-box-shadow-x: -4px;
  --reverse-box-shadow-y: -4px;

  --heading-font-weight: 700;
  --base-font-weight: 500;

  --background: oklch(93.46% 0.0304 254.32);
  --secondary-background: oklch(100% 0 0);
  --foreground: oklch(0% 0 0);
  --main-foreground: oklch(0% 0 0);

  --main: oklch(67.47% 0.1725 259.61);
  --border: oklch(0% 0 0);
  --ring: oklch(0% 0 0);
  --overlay: oklch(0% 0 0 / 0.8);

  --shadow: var(--box-shadow-x) var(--box-shadow-y) 0px 0px var(--border);

  --chart-1: oklch(67.47% 0.1726 259.49);
  --chart-2: oklch(67.28% 0.2147 24.22);
  --chart-3: oklch(86.03% 0.176 92.36);
  --chart-4: oklch(79.76% 0.2044 153.08);
  --chart-5: oklch(66.34% 0.1806 277.2);
  --chart-active-dot: #000;
}

.dark {
  --background: oklch(29.12% 0.0633 270.86);
  --secondary-background: oklch(23.93% 0 0);
  --foreground: oklch(92.49% 0 0);
  --main-foreground: oklch(0% 0 0);

  --border: oklch(0% 0 0);
  --ring: oklch(100% 0 0);

  --shadow: var(--box-shadow-x) var(--box-shadow-y) 0px 0px var(--border);

  --chart-active-dot: #fff;
}

@theme inline {
  --color-main: var(--main);
  --color-background: var(--background);
  --color-secondary-background: var(--secondary-background);
  --color-foreground: var(--foreground);
  --color-main-foreground: var(--main-foreground);
  --color-border: var(--border);
  --color-overlay: var(--overlay);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --spacing-boxShadowX: var(--box-shadow-x);
  --spacing-boxShadowY: var(--box-shadow-y);
  --spacing-reverseBoxShadowX: var(--reverse-box-shadow-x);
  --spacing-reverseBoxShadowY: var(--reverse-box-shadow-y);

  --radius-base: var(--border-radius);

  --shadow-shadow: var(--shadow);
  --shadow-nav: 4px 4px 0px 0px var(--border);

  --font-weight-base: var(--base-font-weight);
  --font-weight-heading: var(--heading-font-weight);

  --spacing-container: 1300px;
}

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
  }

  body {
    @apply text-foreground font-base;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
  }

  #root {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
}

/* Scrollbar Styling */
.scrollbar::-webkit-scrollbar {
  width: 8px;
}

.scrollbar::-webkit-scrollbar-track {
  background: var(--secondary-background);
  border-radius: var(--border-radius);
}

.scrollbar::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Firefox scrollbar */
.scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--secondary-background);
}

/* Hidden scrollbar but still scrollable */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Markdown styling for bot responses */
.markdown-content {
  line-height: 1.6;
  text-align: left;
}

.markdown-content * {
  text-align: left;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: var(--heading-font-weight);
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: var(--foreground);
  text-align: left;
}

.markdown-content h1 { font-size: 1.25rem; }
.markdown-content h2 { font-size: 1.125rem; }
.markdown-content h3 { font-size: 1rem; }

.markdown-content p {
  margin-bottom: 0.75rem;
  color: var(--foreground);
  text-align: left;
}

.markdown-content strong {
  font-weight: var(--heading-font-weight);
  color: var(--foreground);
}

.markdown-content em {
  font-style: italic;
}

.markdown-content code {
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  padding: 0.125rem 0.25rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.875rem;
  color: var(--foreground);
}

.markdown-content pre {
  background-color: var(--background);
  border: 2px solid var(--border);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
  box-shadow: var(--shadow);
}

.markdown-content pre code {
  background: none;
  border: none;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  text-align: left;
}

.markdown-content li {
  margin-bottom: 0.25rem;
  text-align: left;
}

.markdown-content blockquote {
  border-left: 4px solid var(--main);
  padding-left: 1rem;
  margin: 0.75rem 0;
  font-style: italic;
  color: var(--foreground);
  opacity: 0.8;
  text-align: left;
}

.markdown-content a {
  color: var(--main);
  text-decoration: underline;
}

.markdown-content a:hover {
  opacity: 0.8;
}

/* Map status colors */
.status-safe {
  background-color: #22c55e;
  color: white;
}

.status-semi-critical {
  background-color: #fbbf24;
  color: black;
}

.status-critical {
  background-color: #f97316;
  color: white;
}

.status-over-exploited {
  background-color: #dc2626;
  color: white;
}

/* Map legend colors */
.bg-safe {
  background-color: #22c55e;
}

.bg-semi-critical {
  background-color: #fbbf24;
}

.bg-critical {
  background-color: #f97316;
}

.bg-over-exploited {
  background-color: #dc2626;
}

.text-safe {
  color: #22c55e;
}

.text-critical {
  color: #f97316;
}