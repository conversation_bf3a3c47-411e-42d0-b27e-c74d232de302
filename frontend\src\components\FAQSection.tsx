import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import FloatingBackground from './FloatingBackground';

interface FAQItem {
  question: string;
  answer: React.ReactNode;
}

const FAQSection: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqs: FAQItem[] = [
    {
      question: "What is <PERSON><PERSON> & Why was it created?",
      answer: (
        <p>
          Jal Drishti is an AI-powered groundwater resource analysis platform featuring the INGRES AI Chatbot that connects you to groundwater data in multiple Indian languages including Hindi, English, Tamil, Telugu, Bengali, and more.
          This means you can ask questions about groundwater in your native language and INGRES AI will provide intelligent insights.
          You do not need to know SQL or database queries to use <PERSON><PERSON>, just ask in your own language.
          <br />
          <br />
          Jal Dr<PERSON>ti was built to enable water resource managers, policy makers, researchers, and farmers to access groundwater data with ease, in their own language in real-time.
          Our vision is to become the `Groundwater Intelligence Platform` for all water resource management needs across India.
        </p>
      )
    },
    {
      question: "What data sources does <PERSON><PERSON> analyze?",
      answer: (
        <div>
          <p className="mb-2">Jal Drishti's INGRES AI analyzes comprehensive groundwater datasets:</p>
          <ul className="list-disc pl-6 space-y-1">
            <li>155+ data columns covering 2012-2025</li>
            <li>All Indian states and districts</li>
            <li>Rainfall, recharge, and extraction data</li>
            <li>Water quality parameters and contamination levels</li>
            <li>Future projections and availability assessments</li>
          </ul>
          <p className="mt-2">
            Data is sourced from PostgreSQL, MongoDB for chat sessions, and Neo4j for knowledge graph relationships.
          </p>
        </div>
      )
    },
    {
      question: "Which languages does Jal Drishti support?",
      answer: (
        <div>
          <p className="mb-2">Jal Drishti's INGRES AI supports multiple Indian languages:</p>
          <ul className="list-disc pl-6 space-y-1">
            <li>Hindi (हिंदी)</li>
            <li>English</li>
            <li>Tamil (தமிழ்)</li>
            <li>Telugu (తెలుగు)</li>
            <li>Bengali (বাংলা)</li>
            <li>Marathi (मराठी)</li>
            <li>Gujarati (ગુજરાતી)</li>
            <li>Kannada (ಕನ್ನಡ)</li>
          </ul>
          <p className="mt-2">
            Voice input/output is supported for natural conversation in all these languages.
          </p>
        </div>
      )
    },
    {
        question: "How does Jal Drishti ensure data security and privacy?",
        answer: (
          <p>
            Jal Drishti uses industry-standard encryption and secure protocols to protect groundwater data and user information. 
            All data processing happens through secure APIs and the system doesn't store sensitive user queries permanently.
            The INGRES AI processes queries using OpenAI GPT-4o-mini with secure API calls and returns results without storing personal information.
            <br />
            <br />
            PS: <span className="font-bold">Since Jal Drishti is designed for public water resource data</span>, all groundwater information is publicly available and used for the greater good of water resource management.
          </p>
        )
      },

    {
        question: "Is Jal Drishti free to use?",
        answer: (
          <p>
            Yes, Jal Drishti is free to use for all water stakeholders including government agencies, 
            research institutions, farmers, and environmental organizations. The platform is designed 
            to democratize access to groundwater intelligence across India.
          </p>
        )
      },
    {
      question: "How can I access Jal Drishti's INGRES AI?",
      answer: (
        <div>
          <p className="mb-2">To access Jal Drishti's INGRES AI:</p>
          <ol className="list-decimal pl-6 space-y-1">
            <li>Visit the Jal Drishti platform through the web interface</li>
            <li>Choose your preferred language (Hindi, English, Tamil, etc.)</li>
            <li>Ask questions about groundwater data in natural language</li>
            <li>Use voice input for hands-free interaction</li>
            <li>Export results and visualizations for your reports</li>
          </ol>
          <p className="mt-2">
            The platform is designed to be user-friendly for all stakeholders, from farmers to policy makers.
            No technical expertise required - just ask questions in your language!
          </p>
        </div>
      )
    },
  ];

  return (
    <section id="faq" className="py-14 sm:py-16 md:py-20 lg:pt-24 bg-white relative overflow-hidden">
      <FloatingBackground count={12} opacity={0.02} />
      
      <div className="container mx-auto px-4 sm:px-6 md:px-8 max-w-6xl">
        <div className="text-center mb-10 sm:mb-12 md:mb-16">
          <h2 className="text-3xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4">
            Frequently Asked <span className="text-[#5294ff]">Questions</span>
          </h2>
          <p className="text-lg sm:text-lg text-gray-700 max-w-3xl mx-auto px-2">
            Everything you need to know about Jal Drishti and INGRES AI
          </p>
        </div>
        
        <div className="space-y-5 sm:space-y-6 md:space-y-8">
          {faqs.map((faq, index) => (
            <div 
              key={index}
              className="neo-border overflow-hidden transition-all duration-300 w-full"
              style={{ transform: `rotate(${index % 2 === 0 ? '-0.3deg' : '0.3deg'})` }}
            >
              <button
                className="w-full p-5 sm:p-6 text-left flex justify-between items-center bg-[#dcebfe] hover:bg-gray-50 transition-colors duration-200"
                onClick={() => toggleFAQ(index)}
                aria-expanded={openIndex === index}
              >
                <h3 className="font-bold text-lg sm:text-xl">{faq.question}</h3>
                <ChevronDown 
                  className={`w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0 transition-transform duration-300 ${openIndex === index ? 'rotate-180' : ''}`} 
                />
              </button>
              <div 
                className={`overflow-hidden transition-all duration-300 ${
                  openIndex === index ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="p-5 sm:p-6 pt-0 sm:pt-0 bg-[#FFDB58]/5 text-base sm:text-lg text-gray-700">
                  {faq.answer}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection; 