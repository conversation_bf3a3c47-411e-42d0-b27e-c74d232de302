{"name": "neobase-landing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently --names \"FRONTEND-5174,CLIENT-5173\" --prefix-colors \"green,blue\" \"vite --port 5174\" \"npm --prefix ../client run dev -- --port 5173\"", "dev:frontend": "vite --port 5174", "dev:client": "npm --prefix ../client run dev -- --port 5173", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@microsoft/clarity": "^1.0.0", "@types/event-source-polyfill": "^1.0.5", "axios": "^1.7.9", "date-fns": "^4.1.0", "event-source-polyfill": "^1.0.31", "firebase": "^11.4.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-markdown": "^10.0.0", "react-router-dom": "^7.7.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tw-animate-css": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "concurrently": "^9.2.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}