"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, Pie, Pie<PERSON>hart } from "recharts"

import * as React from "react"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface PieChartDonutProps {
  data: Array<{
    [key: string]: any;
    fill?: string;
  }>;
  config: ChartConfig;
  title?: string;
  description?: string;
  dataKey: string;
  nameKey: string;
  centerLabel?: string;
  className?: string;
}

export default function PieChartDonut({
  data,
  config,
  title = "Pie Chart - Donut with Text",
  description = "Data visualization",
  dataKey,
  nameKey,
  centerLabel = "Total",
  className = ""
}: PieChartDonutProps) {
  const totalValue = React.useMemo(() => {
    return data.reduce((acc, curr) => acc + (curr[dataKey] || 0), 0)
  }, [data, dataKey])

  return (
    <Card className={`flex flex-col bg-secondary-background text-foreground ${className}`}>
      <CardHeader className="items-center pb-0">
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={config}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={data}
              dataKey={dataKey}
              nameKey={nameKey}
              innerRadius={60}
              strokeWidth={2}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-3xl font-bold"
                        >
                          {totalValue.toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-foreground"
                        >
                          {centerLabel}
                        </tspan>
                      </text>
                    )
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing data visualization results
        </div>
      </CardFooter>
    </Card>
  )
}
