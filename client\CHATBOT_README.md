# Chatbot Page

## Overview
A beautiful chatbot interface built with React and styled using the neobrutalism design system from the original folder.

## Features

### Layout
- **Split Screen Design**: Left half displays chatbot information, right half contains the chat interface
- **Responsive Design**: Adapts to different screen sizes
- **Neobrutalism Styling**: Bold borders, sharp corners, and high contrast colors

### Left Half - Chatbot Information
- **AI Assistant Card**: Displays chatbot branding and information
- **Custom SVG Image**: Neobrutalism-styled robot illustration
- **Feature Grid**: Highlights key chatbot capabilities (Smart, Fast, Helpful, 24/7)
- **Consistent Theming**: Uses main color scheme with proper contrast

### Right Half - Chat Interface
- **Message History**: Scrollable chat area with user and bot messages
- **Real-time Typing Indicator**: Animated dots when bot is responding
- **Message Input**: Text input with send button
- **Keyboard Support**: Enter key to send messages
- **Auto-scroll**: Automatically scrolls to latest messages

## Design System

### Colors
- **Background**: Light blue-gray (`oklch(93.46% 0.0304 254.32)`)
- **Main Color**: Blue (`oklch(67.47% 0.1725 259.61)`)
- **Borders**: Black (`oklch(0% 0 0)`)
- **Text**: High contrast black/white

### Components Used
- **Button**: Neobrutalism-styled with shadow effects
- **Card**: Container with borders and shadows
- **Input**: Text input with proper styling
- **ScrollArea**: Custom scrollable container

### Typography
- **Headings**: Bold weight (700)
- **Body Text**: Medium weight (500)
- **Font**: System fonts with fallbacks

## Navigation
- **Home Link**: "Back to Home" button in top-left corner
- **Route**: Accessible at `/chatbot`

## Interactive Features
- **Message Sending**: Click send button or press Enter
- **Bot Responses**: Simulated responses with random delays
- **Typing Animation**: Visual feedback during bot response generation
- **Timestamps**: Each message shows send time

## Technical Implementation
- **React Hooks**: useState, useRef, useEffect for state management
- **TypeScript**: Full type safety
- **Responsive Design**: Tailwind CSS classes
- **Component Architecture**: Modular UI components

## Usage
1. Navigate to `/chatbot` route
2. Type a message in the input field
3. Press Enter or click Send button
4. Watch for bot response with typing animation
5. Continue conversation as desired
6. Use "Back to Home" to return to main page

## Customization
The chatbot can be easily customized by:
- Modifying bot response logic in `getBotResponse` function
- Updating colors in the CSS variables
- Changing the robot image in `/public/chatbot-image.svg`
- Adjusting layout proportions in the component structure
