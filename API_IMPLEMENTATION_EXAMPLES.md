# API Implementation Examples & Code Snippets

## Complete API Integration Code

### 1. API Service Layer (`src/lib/api.ts`)

```typescript
// API utility functions for chatbot backend integration

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

export interface NewSessionResponse {
  session_id: string;
}

export interface ChatRequest {
  question: string;
  session_id: string;
  include_visualization: boolean;
}

export interface ChatResponse {
  success: boolean;
  session_id: string;
  sql_query: string;
  response: string;
  explanation: string;
  data: Array<Record<string, any>>;
  csv_data: string;
  error: string;
  visualization: any;
  metadata: {
    rows_returned: number;
    columns: string[];
    execution_time: string;
    has_visualization: boolean;
    session_id: string;
    context_used: boolean;
    context_queries_count: number;
    mentioned_entities_count: number;
  };
  chat_history: any[];
}

export interface DecideResponse {
  srno: number;
  jsonData: any[];
}

/**
 * Creates a new chat session
 */
export async function createNewSession(): Promise<NewSessionResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/new-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating new session:', error);
    throw error;
  }
}

/**
 * Sends a chat message to the backend
 */
export async function sendChatMessage(request: ChatRequest): Promise<ChatResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(data)
    return data;
  } catch (error) {
    console.error('Error sending chat message:', error);
    throw error;
  }
}

/**
 * Sends CSV data to the decide endpoint
 */
export async function sendDecideRequest(csvData: string): Promise<DecideResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/decide`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ csv_content: csvData }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('🎯 Decide Response:', data);
    return data;
  } catch (error) {
    console.error('Error sending decide request:', error);
    throw error;
  }
}
```

### 2. Message Processing Logic

```typescript
// Complete message handling implementation
const handleSend = async () => {
  if (!input.trim() || isLoading) return;

  const currentInput = input.trim();
  const currentAudioUrl = audioPreviewUrl;
  
  // Clear input immediately
  setInput("");
  setAudioPreviewUrl(null);
  setIsLoading(true);

  // Add user message
  const userMessage: Message = {
    id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    type: "user",
    content: currentInput,
    timestamp: new Date(),
    audioUrl: currentAudioUrl || undefined,
  };

  setMessages(prev => [...prev, userMessage]);

  // Handle voice input feedback
  if (audioPreviewUrl) {
    playAvatarAudio('/Recording.wav');
    setIsResponding(true);
    const responseDuration = 2000 + Math.random() * 2000; // 2-4 seconds
    setTimeout(() => {
      setIsResponding(false);
    }, responseDuration);
  }

  try {
    // Ensure we have a session ID
    if (!sessionId) {
      await initializeSession();
      if (!sessionId) {
        throw new Error('Failed to create session');
      }
    }

    console.log('🚀 Sending message to backend:', currentInput);
    const chatResponse: ChatResponse = await sendChatMessage({
      question: currentInput,
      session_id: sessionId,
      include_visualization: true
    });
    console.log('✅ Received backend response:', chatResponse.response.substring(0, 50) + '...');

    // Send CSV data to decide endpoint if available
    let currentDecideData = null;
    if (chatResponse.csv_data) {
      try {
        const decideResponse = await sendDecideRequest(chatResponse.csv_data);
        console.log('🎯 Decide API Response:', decideResponse);

        // Store decide response for dynamic graph rendering
        currentDecideData = {
          graphNumber: decideResponse.srno,
          data: decideResponse.jsonData
        };
        setDecideData(currentDecideData);
        console.log('💾 Storing decideData for message:', currentDecideData);
      } catch (error) {
        console.error('Error calling decide API:', error);
        setDecideData(null);
      }
    } else {
      setDecideData(null);
    }

    // Create analysis bundle if we have structured data
    let bundle: AnalysisBundle | null = null;
    const messageId = `bot_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    if (chatResponse.data && chatResponse.data.length > 0) {
      bundle = createDummyBundle(messageId, chatResponse.visualization, currentInput);
      if (bundle) {
        bundle.messageId = messageId;
        saveBundle(bundle);
      }
    }

    // Save analysis data per message
    if (chatResponse.data) {
      saveAnalysisForMessage(messageId, chatResponse.data);
    }

    // Create bot response message
    const botResponse: Message = {
      id: messageId,
      type: "bot",
      content: chatResponse.response,
      timestamp: new Date(),
      hasChart: !!chatResponse.visualization || !!currentDecideData,
      chartData: chatResponse.visualization,
      analysis: chatResponse.data,
      rightPanelState: {
        decideData: currentDecideData,
        selectedBundle: bundle,
        chartMode: "trend" as const,
        viewMode: "chart" as const
      }
    };

    console.log('🤖 Created bot response:', {
      id: botResponse.id,
      hasChart: botResponse.hasChart,
      hasDecideData: !!currentDecideData,
      decideGraphNumber: currentDecideData?.graphNumber,
      decideDataLength: currentDecideData?.data?.length
    });

    setMessages(prev => {
      const newMessages = [...prev, botResponse];
      console.log('📊 Total messages after adding bot response:', newMessages.length);
      return newMessages;
    });

    // Set up right panel state
    setSelectedMessage(botResponse);
    if (bundle) {
      setSelectedBundle(bundle);
    }
    if (currentDecideData) {
      setDecideData(currentDecideData);
    }
    setSidebarOpen(true);

  } catch (error) {
    console.error('❌ Error sending message to backend:', error);
    console.log('🔄 Falling back to mock response for input:', currentInput);

    // Fallback to mock response if backend is not available
    const botResponse = generateMockResponse(currentInput);
    console.log('🎭 Generated mock response:', botResponse.id, botResponse.content.substring(0, 50) + '...');
    setMessages(prev => {
      const newMessages = [...prev, botResponse];
      console.log('📊 Total messages after adding mock response:', newMessages.length);
      return newMessages;
    });

    // Set up right panel state for mock response
    setSelectedMessage(botResponse);
    if (botResponse.rightPanelState?.selectedBundle) {
      setSelectedBundle(botResponse.rightPanelState.selectedBundle);
    }
    setSidebarOpen(true);
  } finally {
    setIsLoading(false);
  }
};
```

### 3. Local Storage Implementation

```typescript
// Storage keys
const SNAP_KEY = "chat_analysis_bundles_v1";
const MSG_KEY = "chat_messages_v1";

// Read operations
const readBundles = (): AnalysisBundle[] => {
  try {
    const raw = localStorage.getItem(SNAP_KEY);
    return raw ? (JSON.parse(raw) as AnalysisBundle[]) : [];
  } catch {
    return [];
  }
};

const readMessages = (): Message[] => {
  try {
    const raw = localStorage.getItem(MSG_KEY);
    if (!raw) {
      console.log('📭 No messages found in localStorage');
      return [];
    }
    const parsed = JSON.parse(raw) as any[];
    console.log('📖 Raw messages from localStorage:', parsed.length);
    
    // Revive Date objects
    const messages = parsed.map((m) => ({
      ...m,
      timestamp: m.timestamp ? new Date(m.timestamp) : new Date(),
    }));
    
    return messages;
  } catch {
    return [];
  }
};

// Write operations
const writeBundles = (bundles: AnalysisBundle[]) => {
  try {
    localStorage.setItem(SNAP_KEY, JSON.stringify(bundles));
  } catch {}
};

const writeMessages = (msgs: Message[]) => {
  try {
    console.log('💾 Writing to localStorage:', msgs.length, 'messages');
    localStorage.setItem(
      MSG_KEY,
      JSON.stringify(
        msgs.map((m) => ({
          ...m,
          // Store timestamp as ISO string
          timestamp: m.timestamp instanceof Date ? m.timestamp.toISOString() : m.timestamp,
        }))
      )
    );
  } catch (e) {
    console.error('❌ Failed to write messages to localStorage:', e);
  }
};

// Bundle operations
const saveBundle = (bundle: AnalysisBundle) => {
  const bundles = readBundles();
  const idx = bundles.findIndex(s => s.messageId === bundle.messageId);
  if (idx >= 0) bundles[idx] = bundle; 
  else bundles.unshift(bundle);
  writeBundles(bundles);
};

const getBundleByMessageId = (id: string): AnalysisBundle | null => {
  const bundles = readBundles();
  return bundles.find(s => s.messageId === id) || null;
};

// Analysis storage per message
const saveAnalysisForMessage = (messageId: string, analysis: any) => {
  try {
    const key = "chat_analyses";
    const existingRaw = localStorage.getItem(key);
    const map = existingRaw ? JSON.parse(existingRaw) : {};
    map[messageId] = analysis;
    localStorage.setItem(key, JSON.stringify(map));
  } catch (e) {
    console.warn("Failed to save analysis to localStorage", e);
  }
};
```

### 4. Mock Data Generation

```typescript
const generateMockResponse = (query: string): Message => {
  const lowerQuery = query.toLowerCase();
  
  let content = "";
  let hasChart = false;
  let chartData = null;
  let analysis: any = null;

  if (lowerQuery.includes("punjab") || lowerQuery.includes("rajasthan")) {
    content = "Based on the latest data, Punjab shows critical groundwater depletion with 75% of districts classified as over-exploited. The average water level has declined by 2.3 meters annually over the past 5 years.";
    hasChart = true;
    chartData = { type: "trend", region: "punjab" };
    analysis = {
      meta: { prompt: query, createdAt: new Date().toISOString(), region: "rajasthan" },
      trends: {
        type: "trend",
        series: [
          { label: "Jaipur", data: [-0.9,-1.1,-1.3,-1.5,-1.7], years: [2020,2021,2022,2023,2024] },
          { label: "Jodhpur", data: [-0.8,-1.0,-1.2,-1.4,-1.6], years: [2020,2021,2022,2023,2024] }
        ]
      },
      comparison: {
        type: "comparison",
        items: [
          { region: "Jaipur", decline: -1.7 },
          { region: "Bikaner", decline: -1.4 },
          { region: "Udaipur", decline: -0.7 }
        ]
      },
      status: { type: "status", summary: { overExploited: 75, critical: 12, semiCritical: 8, safe: 5 } },
      rawData: [
        { district: "Jaipur", level: -38.4, trend: -1.7 },
        { district: "Jodhpur", level: -35.1, trend: -1.6 },
        { district: "Bikaner", level: -33.2, trend: -1.4 }
      ],
      sql: "SELECT district, avg_water_level, annual_decline FROM rajasthan_gw WHERE year = 2024;",
      explain: "Rajasthan faces severe groundwater stress due to arid climate and intensive agriculture."
    };
  } else if (lowerQuery.includes("national") || lowerQuery.includes("india")) {
    content = "India's groundwater scenario shows mixed trends. While northern states face severe depletion, southern states like Kerala show improvement due to better management practices.";
    hasChart = true;
    chartData = { type: "trend", region: "national" };
    analysis = {
      meta: { prompt: query, createdAt: new Date().toISOString(), region: "national" },
      trends: {
        type: "trend",
        series: [
          { label: "National Avg", data: [ -0.1, -0.2, -0.25, -0.3, -0.28, -0.31 ], years: [2019,2020,2021,2022,2023,2024] }
        ]
      },
      comparison: {
        type: "comparison",
        items: [
          { region: "Punjab", decline: -2.3 },
          { region: "Kerala", decline: 0.5 },
          { region: "Rajasthan", decline: -1.9 }
        ]
      },
      status: {
        type: "status",
        summary: { overExploited: 42, critical: 18, semiCritical: 25, safe: 15 }
      },
      rawData: [
        { state: "Punjab", level: -15.2, trend: -2.3 },
        { state: "Kerala", level: -3.2, trend: 0.5 },
        { state: "Gujarat", level: -8.7, trend: -1.1 }
      ],
      sql: "SELECT state, avg_level, trend FROM national_gw_summary ORDER BY trend DESC;",
      explain: "National analysis shows regional variations in groundwater trends across India."
    };
  } else {
    content = "I can help you analyze groundwater data across India. You can ask about specific states, districts, trends, or comparisons between regions.";
    hasChart = false;
  }

  // Create bundle for mock response if it has chart data
  let bundle: AnalysisBundle | null = null;
  const messageId = `mock_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  if (hasChart && chartData) {
    bundle = createDummyBundle(
      `bundle_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      chartData,
      query,
    );
  }
  if (bundle) {
    bundle.messageId = messageId;
    saveBundle(bundle);
  }

  return {
    id: messageId,
    type: "bot",
    content,
    timestamp: new Date(),
    hasChart,
    chartData,
    analysis,
    rightPanelState: hasChart ? {
      decideData: null, // Mock responses don't have decide data
      selectedBundle: bundle,
      chartMode: "trend" as const,
      viewMode: "chart" as const
    } : undefined,
  };
};
```

This file provides complete, copy-paste ready code examples for implementing the backend integration in your new project.
