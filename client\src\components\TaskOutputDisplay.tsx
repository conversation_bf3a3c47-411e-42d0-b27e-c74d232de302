import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from './ui/card';
import { Badge } from './ui/badge';
import { But<PERSON> } from './ui/button';
import Plotly<PERSON><PERSON> from './PlotlyChart';
import { BarChart3, FileText, Globe, Download, Eye, Code } from 'lucide-react';

interface TaskOutput {
  task_id: string;
  task_title: string;
  type: string;
  status: string;
  execution_result?: any;
  code_generated?: string;
  execution_time?: string;
  findings?: string[];
  sources?: string[];
  summary?: string;
}

interface TaskOutputDisplayProps {
  outputs: TaskOutput[];
  className?: string;
}

const TaskOutputDisplay: React.FC<TaskOutputDisplayProps> = ({ outputs, className }) => {
  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'data_analysis':
        return <BarChart3 className="w-5 h-5" />;
      case 'web_research':
        return <Globe className="w-5 h-5" />;
      case 'synthesis':
        return <FileText className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const getTaskColor = (type: string) => {
    switch (type) {
      case 'data_analysis':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'web_research':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'synthesis':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const downloadData = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!outputs || outputs.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center text-gray-500">
          No task outputs available
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-main" />
            Task Outputs & Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            Detailed outputs from each executed task including data analysis, visualizations, and research findings.
          </p>
        </CardContent>
      </Card>

      {outputs.map((output, index) => (
        <Card key={output.task_id || index} className="overflow-hidden">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getTaskIcon(output.type)}
                <div>
                  <CardTitle className="text-lg">{output.task_title}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={getTaskColor(output.type)}>
                      {output.type.replace('_', ' ')}
                    </Badge>
                    {output.execution_time && (
                      <Badge variant="outline" className="text-xs">
                        {output.execution_time}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadData(output, `${output.task_title.replace(/\s+/g, '_')}_output.json`)}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Download
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Data Analysis Results */}
            {output.type === 'data_analysis' && output.execution_result && (
              <div className="space-y-4">
                {/* Key Insights */}
                {output.execution_result.analysis?.key_insights && (
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <BarChart3 className="w-4 h-4" />
                      Key Insights
                    </h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {output.execution_result.analysis.key_insights.map((insight: string, i: number) => (
                        <li key={i} className="text-gray-700">{insight}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Plotly Visualizations */}
                {output.execution_result.plotly_figures && output.execution_result.plotly_figures.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <BarChart3 className="w-4 h-4" />
                      Data Visualizations
                    </h4>
                    <div className="grid gap-4">
                      {output.execution_result.plotly_figures.map((figure: any, i: number) => (
                        <PlotlyChart
                          key={i}
                          data={figure}
                          title={`Chart ${i + 1}: ${output.task_title}`}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* Statistical Results */}
                {output.execution_result.statistical_results && (
                  <div>
                    <h4 className="font-medium mb-2">Statistical Analysis</h4>
                    <div className="bg-gray-50 rounded p-3">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(output.execution_result.statistical_results, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {/* Generated Code */}
                {output.code_generated && (
                  <details className="bg-gray-50 rounded p-3">
                    <summary className="cursor-pointer font-medium flex items-center gap-2">
                      <Code className="w-4 h-4" />
                      View Generated Code
                    </summary>
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                      <code>{output.code_generated}</code>
                    </pre>
                  </details>
                )}
              </div>
            )}

            {/* Web Research Results */}
            {output.type === 'web_research' && (
              <div className="space-y-4">
                {output.summary && (
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Globe className="w-4 h-4" />
                      Research Summary
                    </h4>
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">{output.summary}</p>
                  </div>
                )}

                {output.findings && output.findings.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Key Findings</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {output.findings.map((finding: string, i: number) => (
                        <li key={i} className="text-gray-700">{finding}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {output.sources && output.sources.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Sources</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {output.sources.map((source: string, i: number) => (
                        <li key={i} className="text-gray-600">{source}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Synthesis Results */}
            {output.type === 'synthesis' && output.execution_result && (
              <div className="space-y-4">
                {output.execution_result.combined_insights && (
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Synthesized Insights
                    </h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {output.execution_result.combined_insights.map((insight: string, i: number) => (
                        <li key={i} className="text-gray-700">{insight}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {output.execution_result.recommendations && (
                  <div>
                    <h4 className="font-medium mb-2">Recommendations</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {output.execution_result.recommendations.map((rec: string, i: number) => (
                        <li key={i} className="text-gray-700">{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Raw Data Display for other types */}
            {!['data_analysis', 'web_research', 'synthesis'].includes(output.type) && output.execution_result && (
              <div>
                <h4 className="font-medium mb-2">Output Data</h4>
                <div className="bg-gray-50 rounded p-3">
                  <pre className="text-xs overflow-x-auto">
                    {JSON.stringify(output.execution_result, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default TaskOutputDisplay;
