import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Bot, User, Info, FileText, BarChart3, Database, Code, Copy, Mic, Play, Pause, Trash2, Square } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { Switch } from './ui/switch';
import { toast } from 'sonner';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from './ui/tabs';

import { createNewSession, sendChatMessage } from '@/lib/api';
import GraphSelector from './graphs/GraphSelector';
import PolicyModeInterface from './PolicyModeInterface';
import TaskOutputDisplay from './TaskOutputDisplay';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  isVoiceMessage?: boolean;
  audioUrl?: string;
  responseData?: {
    sql_query: string;
    explanation: string;
    data: Array<Record<string, any>>;
    csv_data: string;
    user_query: string;
    response_text: string;
    audio_response?: string;
    detected_language?: string;
    input_type?: 'text' | 'voice';
    graph_decision?: {
      number_of_appropriate_graphs: number;
      graph_indices: number[];
      graph_configs: Array<{
        index: number;
        title: string;
        description: string;
        data_keys: {
          primary_key: string;
          value_keys: string[];
          color_key?: string;
        };
        chart_specific: Record<string, any>;
      }>;
    };
  };
}




// LocalStorage functions
const STORAGE_KEYS = {
  MESSAGES: 'chatbot_messages',
  SESSION_ID: 'chatbot_session_id',
};

const saveMessagesToStorage = (messages: Message[]) => {
  try {
    localStorage.setItem(STORAGE_KEYS.MESSAGES, JSON.stringify(messages));
  } catch (error) {
    console.error('Failed to save messages to localStorage:', error);
  }
};

const loadMessagesFromStorage = (): Message[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.MESSAGES);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Convert timestamp strings back to Date objects
      return parsed.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
      }));
    }
  } catch (error) {
    console.error('Failed to load messages from localStorage:', error);
  }
  return [
    {
      id: 1,
      text: "Hello! I'm your AI assistant. How can I help you today?",
      sender: 'bot',
      timestamp: new Date(),
    },
  ];
};

const saveSessionToStorage = (sessionId: string) => {
  try {
    localStorage.setItem(STORAGE_KEYS.SESSION_ID, sessionId);
  } catch (error) {
    console.error('Failed to save session to localStorage:', error);
  }
};

const loadSessionFromStorage = (): string | null => {
  try {
    return localStorage.getItem(STORAGE_KEYS.SESSION_ID);
  } catch (error) {
    console.error('Failed to load session from localStorage:', error);
    return null;
  }
};

const Chatbot: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>(() => loadMessagesFromStorage());
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [currentBotResponse, setCurrentBotResponse] = useState('');
  const [sessionId, setSessionId] = useState<string | null>(() => loadSessionFromStorage());
  const [isInitializingSession, setIsInitializingSession] = useState(false);

  // API Response Data for Sidebar
  const [currentSqlQuery, setCurrentSqlQuery] = useState<string>('');
  const [currentExplanation, setCurrentExplanation] = useState<string>('');
  const [currentData, setCurrentData] = useState<Array<Record<string, any>>>([]);
  
  // Additional data for GraphSelector
  const [currentCsvData, setCurrentCsvData] = useState<string>('');
  const [currentUserQuery, setCurrentUserQuery] = useState<string>('');
  const [currentResponseText, setCurrentResponseText] = useState<string>('');
  const [currentGraphDecision, setCurrentGraphDecision] = useState<any>(null);
  const [isViewingStoredStats, setIsViewingStoredStats] = useState<boolean>(false);
  const [isToggleEnabled, setIsToggleEnabled] = useState<boolean>(false);

  // Policy Mode State
  const [isPolicyMode, setIsPolicyMode] = useState<boolean>(false);
  const [policyQuery, setPolicyQuery] = useState<string>('');
  const [policyReport, setPolicyReport] = useState<any>(null);
  const [taskOutputs, setTaskOutputs] = useState<any[]>([]);

  // Voice Recording State
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlayingRecording, setIsPlayingRecording] = useState(false);
  const [showAudioPreview, setShowAudioPreview] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [playingMessageId, setPlayingMessageId] = useState<number | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingTimerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // Callback to handle graph decision from GraphSelector
  const handleGraphDecisionReady = useCallback((decision: any) => {
    setCurrentGraphDecision(decision);

    // Update the latest bot message with the graph decision
    setMessages(prevMessages => {
      const updatedMessages = [...prevMessages];
      // Find the last bot message
      let lastBotMessageIndex = -1;
      for (let i = updatedMessages.length - 1; i >= 0; i--) {
        if (updatedMessages[i].sender === 'bot') {
          lastBotMessageIndex = i;
          break;
        }
      }

      if (lastBotMessageIndex !== -1 && updatedMessages[lastBotMessageIndex].responseData) {
        updatedMessages[lastBotMessageIndex] = {
          ...updatedMessages[lastBotMessageIndex],
          responseData: {
            ...updatedMessages[lastBotMessageIndex].responseData!,
            graph_decision: decision
          }
        };

        // Save updated messages to localStorage
        saveMessagesToStorage(updatedMessages);
      }

      return updatedMessages;
    });
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize session when component mounts
  const initializeSession = useCallback(async () => {
    if (sessionId || isInitializingSession) return;

    setIsInitializingSession(true);
    try {
      console.log('🚀 Creating new session...');
      const response = await createNewSession();
      setSessionId(response.session_id);
      saveSessionToStorage(response.session_id);
      console.log('✅ Session created:', response.session_id);
    } catch (error) {
      console.error('❌ Failed to create session:', error);
      // Continue without session - can implement fallback logic here
    } finally {
      setIsInitializingSession(false);
    }
  }, [sessionId, isInitializingSession]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    initializeSession();
  }, [initializeSession]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userInput = inputValue;

    // Check if policy mode is enabled and this is a complex query
    if (isToggleEnabled && isComplexPolicyQuery(userInput)) {
      // Switch to policy mode
      setIsPolicyMode(true);
      setPolicyQuery(userInput);
      setInputValue('');

      // Add user message to chat
      const userMessage: Message = {
        id: Date.now(),
        text: userInput,
        sender: 'user',
        timestamp: new Date(),
      };

      setMessages(prev => {
        const newMessages = [...prev, userMessage];
        saveMessagesToStorage(newMessages);
        return newMessages;
      });

      // Add a bot message indicating policy mode activation
      const botMessage: Message = {
        id: Date.now() + 1,
        text: "🔄 **Policy Mode Activated**\n\nYour query has been identified as a complex policy analysis request. I'm now breaking it down into executable tasks and will provide a comprehensive analysis with detailed reports and visualizations.",
        sender: 'bot',
        timestamp: new Date(),
      };

      setMessages(prev => {
        const newMessages = [...prev, botMessage];
        saveMessagesToStorage(newMessages);
        return newMessages;
      });

      return;
    }

    const userMessage: Message = {
      id: Date.now(),
      text: userInput,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => {
      const newMessages = [...prev, userMessage];
      saveMessagesToStorage(newMessages);
      return newMessages;
    });
    setInputValue('');
    setIsTyping(true);

    try {
      // Ensure we have a session ID
      if (!sessionId) {
        console.warn('⚠️ No session ID available, cannot send message');
        setIsTyping(false);
        return;
      }

      // Send message to backend
      console.log('📤 Sending message to backend:', userInput);
      const chatResponse = await sendChatMessage({
        question: userInput,
        input_type: 'text',
        session_id: sessionId,
        include_visualization: true
      });

      // Log the complete response to console
      console.log('📥 Complete Chat Response:', {
        success: chatResponse.success,
        session_id: chatResponse.session_id,
        sql_query: chatResponse.sql_query,
        response: chatResponse.response,
        explanation: chatResponse.explanation,
        data: chatResponse.data,
        csv_data: chatResponse.csv_data,
        error: chatResponse.error,
        visualization: chatResponse.visualization,
        metadata: chatResponse.metadata,
        chat_history: chatResponse.chat_history
      });

      // Store API response data for sidebar
      setCurrentSqlQuery(chatResponse.sql_query || '');
      setCurrentExplanation(chatResponse.explanation || '');
      setCurrentData(chatResponse.data || []);
      setCurrentCsvData(chatResponse.csv_data || '');
      setCurrentUserQuery(userInput);
      setCurrentResponseText(chatResponse.response || '');

      // Clear any previous graph decision to ensure fresh data is fetched
      setCurrentGraphDecision(null);
      setIsViewingStoredStats(false); // This is a new response, not viewing stored stats

      // Display the actual response from the API
      const botMessage: Message = {
        id: Date.now() + 1,
        text: chatResponse.response || "No response received from the server.",
        sender: 'bot',
        timestamp: new Date(),
        responseData: {
          sql_query: chatResponse.sql_query || '',
          explanation: chatResponse.explanation || '',
          data: chatResponse.data || [],
          csv_data: chatResponse.csv_data || '',
          user_query: userInput,
          response_text: chatResponse.response || '',
          graph_decision: undefined, // Will be set by GraphSelector callback
        },
      };
      setMessages(prev => {
        const newMessages = [...prev, botMessage];
        saveMessagesToStorage(newMessages);
        return newMessages;
      });
      setIsTyping(false);
      setIsSidebarOpen(true); // Open sidebar when bot responds

    } catch (error) {
      console.error('❌ Failed to send message:', error);

      // Fallback to mock response
      const botResponseText = getBotResponse(userInput);
      const botMessage: Message = {
        id: Date.now() + 1,
        text: `Error: ${error}. Fallback: ${botResponseText}`,
        sender: 'bot',
        timestamp: new Date(),
      };
      setMessages(prev => {
        const newMessages = [...prev, botMessage];
        saveMessagesToStorage(newMessages);
        return newMessages;
      });
      setIsTyping(false);
    }
  };

  const getBotResponse = (userInput: string): string => {
    const responses = [
      "That's an interesting question! Let me think about that...",
      "I understand what you're asking. Here's what I think...",
      "Great question! Based on what you've told me...",
      "I'd be happy to help you with that. Here's my suggestion...",
      "That's a good point. Let me provide some insights...",
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  // Voice Recording Functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setAudioBlob(audioBlob);
        setAudioUrl(audioUrl);
        setShowAudioPreview(true);
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingDuration(0);
      
      // Start recording timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Error accessing microphone. Please check your permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }
  };

  const playRecording = () => {
    if (audioUrl) {
      const audio = new Audio(audioUrl);
      audioPlayerRef.current = audio;
      
      audio.onended = () => {
        setIsPlayingRecording(false);
      };
      
      audio.play();
      setIsPlayingRecording(true);
    }
  };

  const pauseRecording = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      setIsPlayingRecording(false);
    }
  };

  const deleteRecording = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    setAudioBlob(null);
    setAudioUrl(null);
    setShowAudioPreview(false);
    setIsPlayingRecording(false);
    setRecordingDuration(0);
    
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current = null;
    }
  };

  const sendAudioMessage = async () => {
    if (!audioBlob || !sessionId) return;

    try {
      // Convert audio blob to base64
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64Audio = (reader.result as string).split(',')[1];
        
        const userMessage: Message = {
          id: Date.now(),
          text: '🎤 Voice message',
          sender: 'user',
          timestamp: new Date(),
          isVoiceMessage: true,
          audioUrl: audioUrl || undefined,
        };

        setMessages(prev => {
          const newMessages = [...prev, userMessage];
          saveMessagesToStorage(newMessages);
          return newMessages;
        });

        setIsTyping(true);
        deleteRecording(); // Clear the audio preview

        try {
          const chatResponse = await sendChatMessage({
            audio_data: base64Audio,
            input_type: 'voice',
            session_id: sessionId,
            include_visualization: true
          });

          // Store API response data for sidebar
          setCurrentSqlQuery(chatResponse.sql_query || '');
          setCurrentExplanation(chatResponse.explanation || '');
          setCurrentData(chatResponse.data || []);
          setCurrentCsvData(chatResponse.csv_data || '');
          setCurrentUserQuery('Voice message');
          setCurrentResponseText(chatResponse.response || '');

          let botAudioUrl: string | undefined;
          if (chatResponse.audio_response) {
            try {
              const audioBlob = new Blob([Uint8Array.from(atob(chatResponse.audio_response), c => c.charCodeAt(0))], { type: 'audio/wav' });
              botAudioUrl = URL.createObjectURL(audioBlob);
            } catch (error) {
              console.error('Error creating audio URL:', error);
            }
          }

          const botMessage: Message = {
            id: Date.now() + 1,
            text: chatResponse.response || "No response received from the server.",
            sender: 'bot',
            timestamp: new Date(),
            audioUrl: botAudioUrl,
            responseData: {
              sql_query: chatResponse.sql_query || '',
              explanation: chatResponse.explanation || '',
              data: chatResponse.data || [],
              csv_data: chatResponse.csv_data || '',
              user_query: 'Voice message',
              response_text: chatResponse.response || '',
              audio_response: chatResponse.audio_response,
              detected_language: chatResponse.detected_language,
              input_type: chatResponse.input_type
            },
          };

          setMessages(prev => {
            const newMessages = [...prev, botMessage];
            saveMessagesToStorage(newMessages);
            return newMessages;
          });
        } catch (error) {
          console.error('❌ Error sending voice message:', error);
          const errorMessage: Message = {
            id: Date.now() + 1,
            text: "Sorry, I couldn't process your voice message. Please try again.",
            sender: 'bot',
            timestamp: new Date(),
          };

          setMessages(prev => {
            const newMessages = [...prev, errorMessage];
            saveMessagesToStorage(newMessages);
            return newMessages;
          });
        }
        
        setIsTyping(false);
      };
      
      reader.readAsDataURL(audioBlob);
    } catch (error) {
      console.error('Error processing audio:', error);
      setIsTyping(false);
    }
  };

  // Format recording duration
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Play message audio
  const playMessageAudio = (messageId: number, audioUrl: string) => {
    if (playingMessageId === messageId) {
      // Stop current audio
      if (audioPlayerRef.current) {
        audioPlayerRef.current.pause();
        audioPlayerRef.current = null;
      }
      setPlayingMessageId(null);
    } else {
      // Stop any currently playing audio
      if (audioPlayerRef.current) {
        audioPlayerRef.current.pause();
      }
      
      // Play new audio
      const audio = new Audio(audioUrl);
      audioPlayerRef.current = audio;
      
      audio.onended = () => {
        setPlayingMessageId(null);
      };
      
      audio.onerror = () => {
        console.error('Error playing message audio');
        setPlayingMessageId(null);
      };
      
      audio.play().then(() => {
        setPlayingMessageId(messageId);
      }).catch((error) => {
        console.error('Error playing audio:', error);
        setPlayingMessageId(null);
      });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleViewStats = (responseData: Message['responseData']) => {
    if (responseData) {
      // Load the response data into current state
      setCurrentSqlQuery(responseData.sql_query);
      setCurrentExplanation(responseData.explanation);
      setCurrentData(responseData.data);

      // Load graph data for GraphSelector
      setCurrentCsvData(responseData.csv_data || '');
      setCurrentUserQuery(responseData.user_query || '');
      setCurrentResponseText(responseData.response_text || '');
      setCurrentGraphDecision(responseData.graph_decision || null);
      setIsViewingStoredStats(true); // This is viewing stored stats, not a new response

      // Log for debugging
      console.log('📈 Loaded stats from localStorage (View Stats clicked):', {
        hasData: responseData.data.length > 0,
        hasCsvData: !!responseData.csv_data,
        hasGraphDecision: !!responseData.graph_decision,
        userQuery: responseData.user_query,
        willUseStoredGraphs: !!responseData.graph_decision
      });

      // Open the sidebar to show the stats
      setIsSidebarOpen(true);
    }
  };

  const handleToggleChange = (checked: boolean) => {
    setIsToggleEnabled(checked);
    if (checked) {
      toast.success("Policy mode activated", {
        description: "Enhanced analysis and policy planning features enabled",
        duration: 3000,
      });
    } else {
      toast("Policy mode deactivated", {
        description: "Standard chat mode restored",
        duration: 3000,
      });
      // Reset policy mode state
      setIsPolicyMode(false);
      setPolicyQuery('');
      setPolicyReport(null);
      setTaskOutputs([]);
    }
  };

  // Function to detect complex policy queries
  const isComplexPolicyQuery = (query: string): boolean => {
    const policyKeywords = [
      'plan', 'strategy', 'policy', 'framework', 'roadmap', 'implementation',
      'increase', 'improve', 'develop', 'create', 'establish', 'enhance',
      'groundwater', 'water management', 'conservation', 'sustainability',
      'northeastern states', 'regional', 'comprehensive', 'analysis'
    ];

    const queryLower = query.toLowerCase();
    const keywordMatches = policyKeywords.filter(keyword =>
      queryLower.includes(keyword)
    ).length;

    // Consider it complex if it has multiple keywords and is reasonably long
    return keywordMatches >= 3 && query.length > 50;
  };

  // Handle policy mode completion
  const handlePolicyModeComplete = (report: any) => {
    setPolicyReport(report);
    // Extract task outputs from the report if available
    if (report.visualizations) {
      setTaskOutputs(report.visualizations);
    }
  };

  return (
    <div className={`absolute top-[70px] left-0 right-0 bottom-0 ${isToggleEnabled ? 'bg-blue-50' : 'bg-background'} bg-[linear-gradient(to_right,#80808033_1px,transparent_1px),linear-gradient(to_bottom,#80808033_1px,transparent_1px)] bg-[size:70px_70px] overflow-hidden transition-colors duration-300`}>
      <div className="w-full h-full flex m-0 p-0">
        {/* Left Third - 3D Model Space - Hidden in Policy Mode */}
        {!isToggleEnabled && (
          <div className="w-1/4 h-full flex items-center justify-center p-4 bg-main/10">
            <div className="w-full h-full flex flex-col items-center justify-center space-y-4">
              {/* Placeholder for 3D Model */}
              <div className="w-full max-w-sm h-96 bg-secondary-background rounded-base border-2 border-border flex items-center justify-center shadow-shadow">
                <div className="text-center space-y-4">
                  <div className="w-24 h-24 bg-main rounded-base border-2 border-border flex items-center justify-center mx-auto">
                    <Bot size={48} className="text-main-foreground" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-heading text-foreground">3D Model Space</h3>
                    <p className="text-sm text-foreground/70">
                      3D chatbot model will be displayed here
                    </p>
                  </div>
                </div>
              </div>

              {/* Info Card */}
              <Card className="w-full max-w-sm bg-main text-main-foreground shadow-shadow border-2 border-border">
                <CardHeader className="py-3">
                  <CardTitle className="text-center text-lg">AI Assistant</CardTitle>
                </CardHeader>
                <CardContent className="py-3">
                  <div className="text-center space-y-3">
                    <p className="text-sm opacity-90">
                      Your intelligent conversation partner powered by AI.
                    </p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-secondary-background text-foreground p-2 rounded-base border border-border">
                        <strong>Smart</strong><br />
                        AI responses
                      </div>
                      <div className="bg-secondary-background text-foreground p-2 rounded-base border border-border">
                        <strong>Fast</strong><br />
                        Quick replies
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Right Two-Thirds - Chat (adjusts to 1/3 when sidebar is open, full width in policy mode) */}
        <div className={`${
          isToggleEnabled
            ? (isSidebarOpen ? 'w-3/4' : 'w-full')
            : (isSidebarOpen ? 'w-2/4' : 'w-3/4')
        } h-full flex flex-col p-4 transition-all duration-500 min-w-0`}>
          <Card className="flex-1 flex flex-col shadow-shadow h-full border-2 border-border overflow-hidden min-h-0">
            <CardHeader className="py-3 pb-2">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bot className="w-6 h-6 text-main" />
                  Chat with AI Assistant
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-normal text-foreground/70">Policy Mode</span>
                  <Switch
                    checked={isToggleEnabled}
                    onCheckedChange={handleToggleChange}
                    className="data-[state=checked]:bg-main"
                  />
                </div>
              </CardTitle>
              {/* Session ID Display */}
              <div className="mt-1 text-xs text-foreground/60">
                {isInitializingSession ? (
                  <span className="flex items-center gap-1">
                    <div className="w-3 h-3 border-2 border-main border-t-transparent rounded-full animate-spin"></div>
                    Creating session...
                  </span>
                ) : sessionId ? (
                  <span className="font-mono">
                    Session: {sessionId}
                  </span>
                ) : (
                  <span className="text-red-500">
                    No session (offline mode)
                  </span>
                )}
              </div>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col p-0 min-h-0 overflow-hidden">
              {/* Messages Area */}
              <div className="flex-1 min-h-0 relative">
                <div className="absolute inset-0 overflow-y-auto scrollbar-hide p-6">
                  <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.sender === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-[80%] rounded-base border-2 border-border p-3 ${
                          message.sender === 'user'
                            ? 'bg-main text-main-foreground'
                            : 'bg-secondary-background text-foreground'
                        }`}
                      >
                        <div className="flex items-start gap-2">
                          {message.sender === 'bot' && (
                            <Bot className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          )}
                          {message.sender === 'user' && (
                            <User className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          )}
                          <div className="flex-1">
                            {/* Message Content */}
                            {message.sender === 'bot' ? (
                              <div className="text-sm font-base markdown-content">
                                <ReactMarkdown>{message.text}</ReactMarkdown>
                              </div>
                            ) : (
                              <p className="text-sm font-base">{message.text}</p>
                            )}
                            
                            {/* Audio Playback for Voice Messages */}
                            {message.audioUrl && (
                              <div className="mt-2 flex items-center gap-2 p-2 bg-background/50 rounded border">
                                <Button
                                  variant="noShadow"
                                  size="sm"
                                  onClick={() => playMessageAudio(message.id, message.audioUrl!)}
                                  className="h-6 w-6 p-0"
                                >
                                  {playingMessageId === message.id ? (
                                    <Pause className="w-3 h-3" />
                                  ) : (
                                    <Play className="w-3 h-3" />
                                  )}
                                </Button>
                                <span className="text-xs opacity-70">
                                  {message.sender === 'bot' ? 'AI Voice Response' : 'Your Voice Message'}
                                </span>
                                {message.responseData?.detected_language && (
                                  <span className="text-xs opacity-50">
                                    ({message.responseData.detected_language})
                                  </span>
                                )}
                              </div>
                            )}
                            
                            <div className="flex items-center justify-between mt-1">
                              <div className="flex items-center gap-2">
                                <p className="text-xs opacity-70">
                                  {message.timestamp.toLocaleTimeString()}
                                </p>
                                {message.isVoiceMessage && (
                                  <span className="text-xs opacity-50 flex items-center gap-1">
                                    <Mic className="w-3 h-3" />
                                    Voice
                                  </span>
                                )}
                              </div>
                              {message.sender === 'bot' && message.responseData && (
                                <Button
                                  variant="noShadow"
                                  size="sm"
                                  onClick={() => handleViewStats(message.responseData)}
                                  className="text-xs h-6 px-2"
                                >
                                  <BarChart3 className="w-3 h-3 mr-1" />
                                  {message.responseData.csv_data ? 'View Stats & Charts' : 'View Stats'}
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="bg-secondary-background text-foreground rounded-base border-2 border-border p-3">
                        <div className="flex items-center gap-2">
                          <Bot className="w-4 h-4" />
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-foreground rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                    <div ref={messagesEndRef} />
                  </div>
                </div>
              </div>

              {/* Input Area */}
              <div className="border-t-2 border-border p-6">
                {showAudioPreview ? (
                  /* Audio Preview Mode */
                  <div className="space-y-4">
                    {/* Audio Wave Visualization */}
                    <div className="flex items-center gap-3 p-4 bg-secondary-background rounded-base border-2 border-border">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-6 bg-main rounded animate-pulse"></div>
                        <div className="w-2 h-4 bg-main/80 rounded animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-8 bg-main rounded animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                        <div className="w-2 h-3 bg-main/60 rounded animate-pulse" style={{ animationDelay: '0.3s' }}></div>
                        <div className="w-2 h-7 bg-main rounded animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                        <div className="w-2 h-5 bg-main/80 rounded animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                        <div className="w-2 h-6 bg-main rounded animate-pulse" style={{ animationDelay: '0.6s' }}></div>
                      </div>
                      <div className="flex-1 text-center">
                        <p className="text-sm font-medium">Voice Message</p>
                        <p className="text-xs text-foreground/60">{formatDuration(recordingDuration)}</p>
                      </div>
                    </div>
                    
                    {/* Audio Controls */}
                    <div className="flex gap-2 justify-center">
                      <Button
                        onClick={isPlayingRecording ? pauseRecording : playRecording}
                        variant="noShadow"
                        size="icon"
                      >
                        {isPlayingRecording ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      </Button>
                      <Button
                        onClick={deleteRecording}
                        variant="noShadow"
                        size="icon"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={sendAudioMessage}
                        disabled={isTyping}
                        className="px-4"
                      >
                        <Send className="w-4 h-4 mr-2" />
                        Send Voice Message
                      </Button>
                    </div>
                  </div>
                ) : (
                  /* Normal Input Mode */
                  <div className="flex gap-2">
                    <Input
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type your message here..."
                      className="flex-1"
                      disabled={isTyping || isRecording}
                    />
                    
                    {/* Microphone Button */}
                    <Button
                      onClick={isRecording ? stopRecording : startRecording}
                      disabled={isTyping}
                      size="icon"
                      variant={isRecording ? "default" : "noShadow"}
                      className={isRecording ? "bg-red-600 hover:bg-red-700 border-red-600" : ""}
                    >
                      {isRecording ? (
                        <div className="flex items-center justify-center">
                          <Square className="w-4 h-4" />
                        </div>
                      ) : (
                        <Mic className="w-4 h-4" />
                      )}
                    </Button>
                    
                    {/* Send Button */}
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputValue.trim() || isTyping || isRecording}
                      size="icon"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                )}
                
                {/* Recording Indicator */}
                {isRecording && (
                  <div className="flex items-center justify-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded-base">
                    <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                    <span className="text-sm text-red-700 font-medium">
                      Recording... {formatDuration(recordingDuration)}
                    </span>
                    <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Custom Sidebar */}
        {isSidebarOpen && (
        <div className={`${isToggleEnabled ? 'w-1/4' : 'w-1/4'} h-full border-l-2 border-border bg-secondary-background flex flex-col`}>
          <div className="border-b-2 border-border p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Info className="w-5 h-5 text-main" />
                <h3 className="font-heading font-semibold text-base">Response Analysis</h3>
              </div>
              <Button
                variant="neutral"
                size="sm"
                onClick={() => setIsSidebarOpen(false)}
                className="h-6 w-6 p-0"
              >
                ✕
              </Button>
            </div>
            <p className="text-xs text-foreground/70 mt-1">
              Additional information and insights about the AI response
            </p>
          </div>

          <div className="flex-1 p-4 min-h-0">
            <Tabs defaultValue="chart" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-4 flex-shrink-0">
                <TabsTrigger value="chart" className="flex items-center gap-1">
                  <BarChart3 className="w-3 h-3" />
                  Chart
                </TabsTrigger>
                <TabsTrigger value="rawdata" className="flex items-center gap-1">
                  <Database className="w-3 h-3" />
                  Raw Data
                </TabsTrigger>
                <TabsTrigger value="sql" className="flex items-center gap-1">
                  <Code className="w-3 h-3" />
                  SQL
                </TabsTrigger>
                <TabsTrigger value="explain" className="flex items-center gap-1">
                  <FileText className="w-3 h-3" />
                  Explain
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 mt-4 min-h-0">
                <TabsContent value="chart" className="h-full">
                  {currentCsvData ? (
                    <GraphSelector
                      csvContent={currentCsvData}
                      userQuery={currentUserQuery}
                      responseText={currentResponseText}
                      precomputedGraphDecision={isViewingStoredStats ? currentGraphDecision : undefined}
                      onGraphDecisionReady={handleGraphDecisionReady}
                    />
                  ) : (
                    <Card className="border-2 border-border shadow-shadow h-full flex items-center justify-center">
                      <CardContent className="text-center">
                        <BarChart3 className="w-12 h-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                        <p className="text-muted-foreground font-medium">No chart data available</p>
                        <p className="text-muted-foreground text-sm mt-2">Send a query to generate visualizations</p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="rawdata" className="h-full">
                  <Card className="border-2 border-border shadow-shadow h-full flex flex-col">
                    <CardHeader className="py-3 flex-shrink-0">
                      <CardTitle className="flex items-center justify-between text-sm">
                        Raw Data
                        {currentData.length > 0 && (
                          <Button
                            variant="noShadow"
                            size="sm"
                            onClick={() => navigator.clipboard.writeText(JSON.stringify(currentData, null, 2))}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-2 flex-1 min-h-0 overflow-hidden">
                      {currentData.length > 0 ? (
                        <div className="h-full overflow-auto scrollbar-hide">
                          <table className="w-full text-xs">
                            <thead className="sticky top-0 bg-background">
                              <tr className="bg-main/10 border-b-2 border-border">
                                {Object.keys(currentData[0]).map((key) => (
                                  <th key={key} className="text-left p-3 font-heading font-semibold">
                                    {key}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {currentData.map((row, index) => (
                                <tr key={index} className="border-b border-border hover:bg-secondary-background/50">
                                  {Object.values(row).map((value, valueIndex) => (
                                    <td key={valueIndex} className="p-3 max-w-[200px] truncate" title={String(value)}>
                                      {String(value)}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center text-foreground/60 py-8">
                          <Database className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No data available</p>
                          <p className="text-xs">Send a message to see query results</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="sql" className="h-full">
                  <Card className="border-2 border-border shadow-shadow h-full flex flex-col">
                    <CardHeader className="py-3 flex-shrink-0">
                      <CardTitle className="flex items-center justify-between text-sm">
                        SQL Commands
                        <Button
                          variant="noShadow"
                          size="sm"
                          onClick={() => navigator.clipboard.writeText(currentSqlQuery)}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-2 flex-1 min-h-0 overflow-hidden">
                      <ScrollArea className="h-full">
                        <div className="space-y-3">
                          {currentSqlQuery ? (
                            <div className="relative h-full">
                              <pre className="bg-secondary-background p-4 rounded-base border-2 border-border text-sm font-mono overflow-auto h-full min-h-[200px] whitespace-pre-wrap">
                                <code>{currentSqlQuery}</code>
                              </pre>
                              <Button
                                variant="noShadow"
                                size="sm"
                                className="absolute top-3 right-3 h-6 w-6 p-0"
                                onClick={() => navigator.clipboard.writeText(currentSqlQuery)}
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="text-center text-foreground/60 py-8">
                              <Code className="w-8 h-8 mx-auto mb-2 opacity-50" />
                              <p className="text-sm">No SQL query available</p>
                              <p className="text-xs">Send a message to see the generated SQL</p>
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="explain" className="h-full">
                  <Card className="border-2 border-border shadow-shadow h-full flex flex-col">
                    <CardHeader className="py-3 flex-shrink-0">
                      <CardTitle className="text-sm">Response Explanation</CardTitle>
                    </CardHeader>
                    <CardContent className="py-2 flex-1 min-h-0 overflow-hidden">
                      <ScrollArea className="h-full">
                        <div className="prose prose-sm max-w-none">
                          {currentExplanation ? (
                            <div className="text-xs leading-relaxed text-foreground markdown-content">
                              <ReactMarkdown>{currentExplanation}</ReactMarkdown>
                            </div>
                          ) : (
                            <div className="text-center text-foreground/60 py-8">
                              <Info className="w-8 h-8 mx-auto mb-2 opacity-50" />
                              <p className="text-sm">No explanation available</p>
                              <p className="text-xs">Send a message to see the AI explanation</p>
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
        )}
      </div>
    </div>
  );
};

export default Chatbot;
