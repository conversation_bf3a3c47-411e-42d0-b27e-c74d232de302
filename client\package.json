{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/typography": "^0.5.19", "@tailwindcss/vite": "^4.1.13", "@tanstack/react-table": "^8.21.3", "@types/leaflet": "^1.9.20", "@types/papaparse": "^5.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.22", "html2canvas": "^1.4.1", "jspdf": "^3.0.3", "leaflet": "^1.9.4", "lucide-react": "^0.544.0", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "plotly.js": "^3.1.0", "plotly.js-dist-min": "^3.1.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-leaflet": "^5.0.0", "react-markdown": "^10.1.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.9.2", "recharts": "^3.2.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.5.2", "@types/plotly.js": "^3.0.6", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^5.0.3", "babel-plugin-react-compiler": "^19.1.0-rc.3", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "tw-animate-css": "^1.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.44.0", "vite": "^7.1.7"}}