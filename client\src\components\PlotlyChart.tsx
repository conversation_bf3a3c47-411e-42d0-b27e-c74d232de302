import React, { useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from './ui/card';

interface PlotlyChartProps {
  data: any;
  title?: string;
  className?: string;
}

const PlotlyChart: React.FC<PlotlyChartProps> = ({ data, title, className }) => {
  const plotRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!data || !plotRef.current) return;

    // Dynamically import Plotly to avoid SSR issues
    import('plotly.js-dist-min').then((Plotly) => {
      if (plotRef.current) {
        // Clear any existing plot
        Plotly.purge(plotRef.current);
        
        // Create new plot
        Plotly.newPlot(
          plotRef.current,
          data.data || data,
          data.layout || {
            title: title,
            autosize: true,
            margin: { l: 50, r: 50, t: 50, b: 50 },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
          },
          {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false,
          }
        );
      }
    }).catch((error) => {
      console.error('Error loading Plotly:', error);
    });

    // Cleanup function
    return () => {
      if (plotRef.current) {
        import('plotly.js-dist-min').then((Plotly) => {
          if (plotRef.current) {
            Plotly.purge(plotRef.current);
          }
        });
      }
    };
  }, [data, title]);

  if (!data) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center text-gray-500">
          No chart data available
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent>
        <div 
          ref={plotRef} 
          className="w-full h-96"
          style={{ minHeight: '400px' }}
        />
      </CardContent>
    </Card>
  );
};

export default PlotlyChart;
