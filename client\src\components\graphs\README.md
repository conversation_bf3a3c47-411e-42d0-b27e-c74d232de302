# Graph Components

This folder contains 6 reusable graph components that accept data as props. Each component is designed to be flexible and customizable.

## Components

### 1. BarChartActive
A bar chart with an active/highlighted bar.

```tsx
import { BarChartActive } from '@/components/graphs';

const data = [
  { browser: "chrome", visitors: 187, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
  { browser: "firefox", visitors: 275, fill: "var(--color-firefox)" },
];

const config = {
  visitors: { label: "Visitors" },
  chrome: { label: "Chrome", color: "var(--chart-1)" },
  safari: { label: "Safari", color: "var(--chart-2)" },
  firefox: { label: "Firefox", color: "var(--chart-3)" },
};

<BarChartActive
  data={data}
  config={config}
  dataKey="visitors"
  nameKey="browser"
  activeIndex={2}
  title="Browser Usage"
  description="January - June 2024"
/>
```

### 2. BarChartStacked
A stacked bar chart with legend.

```tsx
import { BarChartStacked } from '@/components/graphs';

const data = [
  { month: "January", desktop: 186, mobile: 80 },
  { month: "February", desktop: 305, mobile: 200 },
];

const config = {
  desktop: { label: "Desktop", color: "var(--chart-1)" },
  mobile: { label: "Mobile", color: "var(--chart-2)" },
};

<BarChartStacked
  data={data}
  config={config}
  xAxisKey="month"
  stackedKeys={["desktop", "mobile"]}
  title="Device Usage"
/>
```

### 3. PieChartDonut
A donut pie chart with center text.

```tsx
import { PieChartDonut } from '@/components/graphs';

const data = [
  { browser: "chrome", visitors: 275, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
];

const config = {
  visitors: { label: "Visitors" },
  chrome: { label: "Chrome", color: "var(--chart-1)" },
  safari: { label: "Safari", color: "var(--chart-2)" },
};

<PieChartDonut
  data={data}
  config={config}
  dataKey="visitors"
  nameKey="browser"
  centerLabel="Total"
  title="Browser Distribution"
/>
```

### 4. LineChartDots
A line chart with colored dots.

```tsx
import { LineChartDots } from '@/components/graphs';

const data = [
  { browser: "chrome", visitors: 275, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
];

const config = {
  visitors: { label: "Visitors", color: "var(--chart-2)" },
  chrome: { label: "Chrome", color: "var(--chart-1)" },
  safari: { label: "Safari", color: "var(--chart-2)" },
};

<LineChartDots
  data={data}
  config={config}
  dataKey="visitors"
  nameKey="browser"
  title="Trend Analysis"
/>
```

### 5. LineChartInteractive
An interactive line chart with toggleable data series.

```tsx
import { LineChartInteractive } from '@/components/graphs';

const data = [
  { date: "2024-04-01", desktop: 222, mobile: 150 },
  { date: "2024-04-02", desktop: 97, mobile: 180 },
];

const config = {
  views: { label: "Page Views" },
  desktop: { label: "Desktop", color: "var(--chart-1)" },
  mobile: { label: "Mobile", color: "var(--chart-2)" },
};

<LineChartInteractive
  data={data}
  config={config}
  xAxisKey="date"
  interactiveKeys={["desktop", "mobile"]}
  title="Interactive Analytics"
/>
```

### 6. LineChartMultiple
A line chart with multiple data series.

```tsx
import { LineChartMultiple } from '@/components/graphs';

const data = [
  { month: "January", desktop: 186, mobile: 80 },
  { month: "February", desktop: 305, mobile: 200 },
];

const config = {
  desktop: { label: "Desktop", color: "var(--chart-1)" },
  mobile: { label: "Mobile", color: "var(--chart-2)" },
};

<LineChartMultiple
  data={data}
  config={config}
  xAxisKey="month"
  lineKeys={["desktop", "mobile"]}
  title="Multi-Series Analysis"
/>
```

## Common Props

All components accept these common props:
- `data`: Array of data objects
- `config`: ChartConfig object for styling and labels
- `title`: Chart title (optional)
- `description`: Chart description (optional)
- `className`: Additional CSS classes (optional)

## Data Format

Each component expects data in a specific format. Make sure your data matches the expected structure for each component type.
