"use client"

import { TrendingUp } from "lucide-react"
import { CartesianGrid, Line, LineChart, XAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface LineChartMultipleProps {
  data: Array<{
    [key: string]: any;
  }>;
  config: ChartConfig;
  title?: string;
  description?: string;
  xAxisKey: string;
  lineKeys: string[];
  className?: string;
}

export default function LineChartMultiple({
  data,
  config,
  title = "Line Chart - Multiple",
  description = "Data visualization",
  xAxisKey,
  lineKeys,
  className = ""
}: LineChartMultipleProps) {
  return (
    <Card className={`bg-secondary-background text-foreground ${className}`}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          className="[&.recharts-layer_path]:stroke-black [&.recharts-layer_path]:dark:stroke-white"
          config={config}
        >
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey={xAxisKey}
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => 
                typeof value === 'string' && value.length > 3 
                  ? value.slice(0, 3) 
                  : value
              }
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            {lineKeys.map((key) => (
              <Line
                key={key}
                dataKey={key}
                type="monotone"
                stroke={`var(--color-${key})`}
                strokeWidth={2}
                dot={false}
              />
            ))}
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Showing data visualization results
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
