# Data Structures & Sample Responses

## Core Data Interfaces

### 1. Message Structure
```typescript
interface Message {
  id: string;                    // Unique identifier: "bot_1703123456789_abc123def"
  type: "user" | "bot";         // Message sender type
  content: string;              // Main message text content
  timestamp: Date;              // When message was created
  hasChart?: boolean;           // Whether message includes visualization
  chartData?: any;              // Chart configuration object
  audioUrl?: string;            // Voice message file URL
  analysis?: any;               // Raw analysis data from backend
  rightPanelState?: {           // UI state for persistence
    decideData?: {graphNumber: number, data: any[]} | null;
    selectedBundle?: AnalysisBundle | null;
    chartMode?: "trend" | "compare" | "status";
    viewMode?: "chart" | "map";
  };
}
```

### 2. Analysis Bundle Structure
```typescript
interface AnalysisBundle {
  messageId: string;            // Links to specific message
  createdAt: string;           // ISO timestamp
  queryText: string;           // Original user query
  sections: {
    trend: AnalysisSection;
    comparison: AnalysisSection;
    status: AnalysisSection;
  };
}

interface AnalysisSection {
  chart: {
    type: "trend" | "comparison" | "district" | "status";
    region?: string;
  };
  rawData: Array<Record<string, string | number>>;
  sql: string;
  explain: string;
}
```

## Sample Backend Responses

### 1. Session Creation Response
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 2. Chat API Response - Trend Analysis
**Query**: "Show groundwater trends in Punjab over last 5 years"

```json
{
  "success": true,
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "sql_query": "SELECT year, district, avg_water_level_m, annual_decline_rate FROM punjab_groundwater WHERE year >= 2019 ORDER BY year, district",
  "response": "Punjab shows alarming groundwater depletion trends. Over the past 5 years (2019-2024), the average water level has declined from -12.5m to -18.7m, representing a 33% deterioration. The annual decline rate has accelerated from 2.1% to 3.5%, with Ludhiana and Amritsar districts showing the steepest declines. This trend is primarily attributed to intensive rice-wheat cropping patterns and inadequate recharge mechanisms.",
  "explanation": "The analysis examines district-wise groundwater monitoring data from Punjab State Water Resources Department, focusing on pre-monsoon and post-monsoon water level measurements across 142 monitoring wells.",
  "data": [
    {
      "year": 2019,
      "district": "Ludhiana", 
      "avg_water_level_m": -12.5,
      "annual_decline_rate": 2.1,
      "category": "Over-exploited",
      "recharge_mcm": 1250.5,
      "extraction_mcm": 2100.8
    },
    {
      "year": 2020,
      "district": "Ludhiana",
      "avg_water_level_m": -13.8,
      "annual_decline_rate": 2.8,
      "category": "Over-exploited", 
      "recharge_mcm": 1180.2,
      "extraction_mcm": 2250.1
    },
    {
      "year": 2021,
      "district": "Ludhiana",
      "avg_water_level_m": -15.2,
      "annual_decline_rate": 3.1,
      "category": "Over-exploited",
      "recharge_mcm": 1095.8,
      "extraction_mcm": 2380.5
    }
  ],
  "csv_data": "year,district,avg_water_level_m,annual_decline_rate,category,recharge_mcm,extraction_mcm\n2019,Ludhiana,-12.5,2.1,Over-exploited,1250.5,2100.8\n2020,Ludhiana,-13.8,2.8,Over-exploited,1180.2,2250.1\n2021,Ludhiana,-15.2,3.1,Over-exploited,1095.8,2380.5",
  "error": "",
  "visualization": {
    "type": "trend",
    "title": "Punjab Groundwater Level Trends",
    "config": {
      "xAxis": "year",
      "yAxis": "avg_water_level_m", 
      "groupBy": "district",
      "chartType": "line"
    }
  },
  "metadata": {
    "rows_returned": 15,
    "columns": ["year", "district", "avg_water_level_m", "annual_decline_rate", "category", "recharge_mcm", "extraction_mcm"],
    "execution_time": "0.34s",
    "has_visualization": true,
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "context_used": true,
    "context_queries_count": 2,
    "mentioned_entities_count": 1
  },
  "chat_history": [
    {
      "role": "user",
      "content": "Show groundwater trends in Punjab over last 5 years",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 3. Decide API Response
**Input**: CSV data from chat response

```json
{
  "srno": 1,
  "jsonData": [
    {
      "year": "2019-2020",
      "state": "PUNJAB",
      "district": "LUDHIANA",
      "rainfall_mm_total": "756.23",
      "annual_ground_water_recharge_ham_total": "1250.50",
      "geo_area_total_ham": "3500.75",
      "ground_water_extraction_total_ham": "2100.80",
      "stage_of_extraction_percent": "168.12",
      "category": "Over-exploited"
    },
    {
      "year": "2020-2021", 
      "state": "PUNJAB",
      "district": "LUDHIANA",
      "rainfall_mm_total": "698.45",
      "annual_ground_water_recharge_ham_total": "1180.20",
      "geo_area_total_ham": "3500.75",
      "ground_water_extraction_total_ham": "2250.10",
      "stage_of_extraction_percent": "190.75",
      "category": "Over-exploited"
    }
  ]
}
```

### 4. Comparison Query Response
**Query**: "Compare groundwater status between Punjab and Kerala"

```json
{
  "success": true,
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "sql_query": "SELECT state, category, COUNT(*) as district_count, AVG(stage_of_extraction) as avg_extraction FROM district_groundwater_status WHERE state IN ('Punjab', 'Kerala') GROUP BY state, category ORDER BY state, avg_extraction DESC",
  "response": "The comparison between Punjab and Kerala reveals stark contrasts in groundwater management. Punjab has 75% of its districts (18 out of 24) classified as over-exploited with an average extraction rate of 165%, while Kerala shows much better management with only 15% over-exploited districts (2 out of 14) and an average extraction rate of 45%. Kerala's success is attributed to better rainwater harvesting, traditional water conservation practices, and diversified cropping patterns.",
  "explanation": "This comparative analysis uses the Central Ground Water Board's district-wise assessment data, examining stage of groundwater extraction and categorization across both states.",
  "data": [
    {
      "state": "Punjab",
      "category": "Over-exploited",
      "district_count": 18,
      "avg_extraction": 165.2,
      "total_districts": 24
    },
    {
      "state": "Punjab", 
      "category": "Critical",
      "district_count": 4,
      "avg_extraction": 85.5,
      "total_districts": 24
    },
    {
      "state": "Punjab",
      "category": "Safe",
      "district_count": 2,
      "avg_extraction": 45.8,
      "total_districts": 24
    },
    {
      "state": "Kerala",
      "category": "Over-exploited", 
      "district_count": 2,
      "avg_extraction": 95.3,
      "total_districts": 14
    },
    {
      "state": "Kerala",
      "category": "Semi-Critical",
      "district_count": 4,
      "avg_extraction": 65.7,
      "total_districts": 14
    },
    {
      "state": "Kerala",
      "category": "Safe",
      "district_count": 8,
      "avg_extraction": 25.4,
      "total_districts": 14
    }
  ],
  "csv_data": "state,category,district_count,avg_extraction,total_districts\nPunjab,Over-exploited,18,165.2,24\nPunjab,Critical,4,85.5,24\nPunjab,Safe,2,45.8,24\nKerala,Over-exploited,2,95.3,14\nKerala,Semi-Critical,4,65.7,14\nKerala,Safe,8,25.4,14",
  "visualization": {
    "type": "comparison",
    "title": "Punjab vs Kerala Groundwater Status",
    "config": {
      "xAxis": "state",
      "yAxis": "district_count",
      "groupBy": "category",
      "chartType": "bar"
    }
  },
  "metadata": {
    "rows_returned": 6,
    "columns": ["state", "category", "district_count", "avg_extraction", "total_districts"],
    "execution_time": "0.28s",
    "has_visualization": true,
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "context_used": true,
    "context_queries_count": 1,
    "mentioned_entities_count": 2
  }
}
```

## Graph Data Formats

### 1. Graph 1 (Time Series) Expected Format
```javascript
[
  {
    "year": "2019-2020",
    "state": "PUNJAB", 
    "district": "LUDHIANA",
    "rainfall_mm_total": "756.23",
    "annual_ground_water_recharge_ham_total": "1250.50",
    "geo_area_total_ham": "3500.75",
    "ground_water_extraction_total_ham": "2100.80"
  }
]
```

### 2. Graph 3 (Area Chart) Expected Format
```javascript
[
  {
    "date": "2020-01",
    "value1": 1250.5,
    "value2": 2100.8,
    "category": "Recharge vs Extraction"
  }
]
```

### 3. Graph 6 (Bar Chart) Expected Format
```javascript
[
  {
    "category": "Over-exploited",
    "count": 18,
    "percentage": 75.0,
    "state": "Punjab"
  }
]
```

## Local Storage Data Examples

### 1. Stored Messages
```json
[
  {
    "id": "user_1703123456789_abc123",
    "type": "user", 
    "content": "Show groundwater trends in Punjab",
    "timestamp": "2024-01-15T10:30:00.000Z"
  },
  {
    "id": "bot_1703123456890_def456",
    "type": "bot",
    "content": "Punjab shows alarming groundwater depletion trends...",
    "timestamp": "2024-01-15T10:30:05.000Z",
    "hasChart": true,
    "chartData": {"type": "trend", "region": "punjab"},
    "rightPanelState": {
      "decideData": {
        "graphNumber": 1,
        "data": [...]
      },
      "chartMode": "trend",
      "viewMode": "chart"
    }
  }
]
```

### 2. Stored Analysis Bundles
```json
[
  {
    "messageId": "bot_1703123456890_def456",
    "createdAt": "2024-01-15T10:30:05.000Z",
    "queryText": "Show groundwater trends in Punjab",
    "sections": {
      "trend": {
        "chart": {"type": "trend", "region": "punjab"},
        "rawData": [
          {"year": 2019, "level": -12.5, "trend": -2.1},
          {"year": 2020, "level": -13.8, "trend": -2.8}
        ],
        "sql": "SELECT year, avg_water_level...",
        "explain": "Punjab groundwater analysis shows declining trends..."
      }
    }
  }
]
```

## Error Response Examples

### 1. Backend Error Response
```json
{
  "success": false,
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "sql_query": "",
  "response": "",
  "explanation": "",
  "data": [],
  "csv_data": "",
  "error": "Database connection timeout. Please try again.",
  "visualization": null,
  "metadata": {
    "rows_returned": 0,
    "columns": [],
    "execution_time": "5.00s",
    "has_visualization": false,
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "context_used": false,
    "context_queries_count": 0,
    "mentioned_entities_count": 0
  },
  "chat_history": []
}
```

### 2. Session Creation Error
```json
{
  "error": "Service temporarily unavailable",
  "message": "Unable to create new session. Please try again later.",
  "status": 503
}
```

This document provides all the data structure examples and sample responses needed to understand and implement the backend integration.
